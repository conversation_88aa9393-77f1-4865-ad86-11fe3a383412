[package]
name = "btql"
version = "0.1.0"
edition = "2021"

[dependencies]
tantivy = { path = "../tantivy", optional = true }
util = { path = "../util" }

lazy_static = "1.5.0"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
time = { version = "0.3.37", features = ["formatting", "macros"] }
log = "0.4.22"
base64 = "0.22.1"
thiserror = "1.0"
regex = "1.11.0"
tantivy_common = { package = "tantivy-common", git = "https://github.com/braintrustdata/tantivy.git", rev = "d76ec2848821b8845d593577133aa72e7777482f" }


[features]
default = ["tantivy"]
tantivy = ["dep:tantivy"]
wasm = ["time/wasm-bindgen"]
