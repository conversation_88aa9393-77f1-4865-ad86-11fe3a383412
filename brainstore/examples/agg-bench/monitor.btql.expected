[{"error": null, "query": "-- token query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count", "result_rows": [{"completion_tokens": 1558, "count": 3, "prompt_tokens": 3187, "time": "2025-07-24T00:00:00Z"}, {"completion_tokens": 2216, "count": 3, "prompt_tokens": 488, "time": "2025-07-26T00:00:00Z"}, {"completion_tokens": 474, "count": 1, "prompt_tokens": 1640, "time": "2025-07-27T00:00:00Z"}, {"completion_tokens": 494, "count": 1, "prompt_tokens": 1649, "time": "2025-07-22T00:00:00Z"}, {"completion_tokens": 83, "count": 1, "prompt_tokens": 167, "time": "2025-07-28T00:00:00Z"}, {"completion_tokens": 872, "count": 1, "prompt_tokens": 579, "time": "2025-07-25T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- cost query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time, metadata.model AS model | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count", "result_rows": [{"completion_tokens": 2216, "count": 3, "prompt_tokens": 488, "time": "2025-07-26T00:00:00Z"}, {"completion_tokens": 320, "count": 1, "prompt_tokens": 673, "time": "2025-07-24T00:00:00Z"}, {"completion_tokens": 413, "count": 1, "model": "gpt-3.5-turbo", "prompt_tokens": 1948, "time": "2025-07-24T00:00:00Z"}, {"completion_tokens": 474, "count": 1, "prompt_tokens": 1640, "time": "2025-07-27T00:00:00Z"}, {"completion_tokens": 494, "count": 1, "prompt_tokens": 1649, "time": "2025-07-22T00:00:00Z"}, {"completion_tokens": 825, "count": 1, "model": "claude-3-opus", "prompt_tokens": 566, "time": "2025-07-24T00:00:00Z"}, {"completion_tokens": 83, "count": 1, "model": "claude-3-sonnet", "prompt_tokens": 167, "time": "2025-07-28T00:00:00Z"}, {"completion_tokens": 872, "count": 1, "model": "claude-3-sonnet", "prompt_tokens": 579, "time": "2025-07-25T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- latency query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS c, percentile(metrics.end-metrics.start, 0.5) AS p50, percentile(is_root ? metrics.end-metrics.start : null, 0.5) AS p50_root, percentile(is_root ? metrics.end-metrics.start : null, 0.95) AS p95_root", "result_rows": [{"c": 1, "p50": 0.402492, "p50_root": 0.402492, "p95_root": 0.402492, "time": "2025-07-25T00:00:00Z"}, {"c": 1, "p50": 2.857608, "p50_root": 2.857608, "p95_root": 2.857608, "time": "2025-07-28T00:00:00Z"}, {"c": 1, "p50": 3.353452, "p50_root": 3.353452, "p95_root": 3.353452, "time": "2025-07-27T00:00:00Z"}, {"c": 1, "p50": 3.935334, "p50_root": 3.935334, "p95_root": 3.935334, "time": "2025-07-22T00:00:00Z"}, {"c": 3, "p50": 0.444824, "p50_root": 0.444824, "p95_root": 0.444824, "time": "2025-07-26T00:00:00Z"}, {"c": 3, "p50": 3.034319, "p50_root": 3.034319, "p95_root": 3.034319, "time": "2025-07-24T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- ttft query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: percentile(metrics.time_to_first_token, 0.5) AS p50_time_to_first_token, percentile(metrics.time_to_first_token, 0.95) AS p95_time_to_first_token", "result_rows": [{"time": "2025-07-22T00:00:00Z"}, {"time": "2025-07-24T00:00:00Z"}, {"time": "2025-07-25T00:00:00Z"}, {"time": "2025-07-26T00:00:00Z"}, {"time": "2025-07-27T00:00:00Z"}, {"time": "2025-07-28T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- request count query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS count, sum(is_root ? 1 : 0) as ternary_count, count(1) AS spans, sum(span_attributes.type = 'llm' ? 1 : 0) AS llm_spans, sum(span_attributes.type = 'tool' ? 1 : 0) AS tool_count", "result_rows": [{"count": 1, "llm_spans": 0, "spans": 1, "ternary_count": 1, "time": "2025-07-22T00:00:00Z", "tool_count": 0}, {"count": 1, "llm_spans": 0, "spans": 1, "ternary_count": 1, "time": "2025-07-25T00:00:00Z", "tool_count": 0}, {"count": 1, "llm_spans": 0, "spans": 1, "ternary_count": 1, "time": "2025-07-27T00:00:00Z", "tool_count": 0}, {"count": 1, "llm_spans": 0, "spans": 1, "ternary_count": 1, "time": "2025-07-28T00:00:00Z", "tool_count": 0}, {"count": 3, "llm_spans": 0, "spans": 3, "ternary_count": 3, "time": "2025-07-24T00:00:00Z", "tool_count": 0}, {"count": 3, "llm_spans": 0, "spans": 3, "ternary_count": 3, "time": "2025-07-26T00:00:00Z", "tool_count": 0}], "skip": false}]