use std::{collections::HashSet, time::Duration};

use clap::Parser;
use otel_common::process_info::get_system_memory;
use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use tracing::instrument;

use crate::{
    config_with_store::{ConfigWithStore, StoreInfo},
    directory::cached_directory::FileCacheOpts,
    global_store::{GlobalStore, RecentObject, SegmentWalEntriesXactIdStatistic},
    index_document::make_full_schema,
    limits::{default_max_concurrent_index_operations, global_limits},
    merge::{
        default_target_num_segments, merge_tantivy_segments, MergeOpts, MergeTantivySegmentsInput,
        MergeTantivySegmentsOptionalInput, MergeTantivySegmentsOptions,
    },
    process_wal::{
        clear_compacted_index, compact_segment_wal, ClearCompactedIndexInput,
        CompactSegmentWalInput, CompactSegmentWalOptionalInput, CompactSegmentWalOptions,
        ProcessObjectWalOptions, XACT_ID_FIELD,
    },
    tantivy_index::{validate_tantivy_index, TantivyIndexScope, TantivyIndexWriterOpts},
};
use util::{
    anyhow::{anyhow, Result},
    config::StorageConfig,
    futures::join,
    schema::Schema,
    system_types::{make_object_schema, FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

#[derive(Clone)]
pub struct OptimizeObjectInput<'a> {
    pub segment_ids: &'a [Uuid],
    pub config: ConfigWithStore,
    pub schema: util::schema::Schema,
    pub dry_run: bool,
    pub recompact: bool,
    pub run_async: bool,

    // These come in via input because they get parsed elsewhere.
    pub file_cache_opts: FileCacheOpts,
    pub storage_config: StorageConfig,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize, Default)]
pub struct OptimizeObjectOptions {
    /// Configuration options for writing to the segment index.
    #[command(flatten)]
    #[serde(flatten)]
    pub process_wal_opts: ProcessObjectWalOptions,

    /// Configuration options for writing to the segment index.
    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_opts: CompactSegmentWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub merge_opts: MergeOpts,

    #[command(flatten)]
    #[serde(flatten)]
    pub tuning_opts: OptimizeObjectTuningOptions,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct OptimizeObjectTuningOptions {
    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_merge_page_size(),
        env = "BRAINSTORE_MERGE_PAGE_SIZE",
        help = format!(
            "The page size for the merge operation (defaults to {})",
            util::ByteSize::from(default_merge_page_size())
        )
    )]
    #[serde(default = "default_merge_page_size")]
    pub merge_page_size: usize,

    #[arg(
        long,
        value_parser = util::ByteSize::parse_to_usize,
        default_value_t = default_merge_memory_limit(),
        env = "BRAINSTORE_MERGE_MEMORY_LIMIT",
        help = format!(
            "The memory limit for the merge operation (defaults to {})",
            util::ByteSize::from(default_merge_memory_limit())
        )
    )]
    #[serde(default = "default_merge_memory_limit")]
    pub merge_memory_limit: usize,
}

impl Default for OptimizeObjectTuningOptions {
    fn default() -> Self {
        Self {
            merge_page_size: default_merge_page_size(),
            merge_memory_limit: default_merge_memory_limit(),
        }
    }
}

fn default_merge_page_size() -> usize {
    16 * 1024 * 1024 /* 16 MB */
}

fn default_merge_memory_limit() -> usize {
    std::cmp::min(
        8 * 1024 * 1024 * 1024, /* 8 GB */
        get_system_memory() / default_max_concurrent_index_operations(),
    )
}

// One day.
pub const OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS: u64 = 24 * 60 * 60;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeObjectOutput {
    pub performed: Vec<PerformedOptimizationStep>,
    pub planned: Vec<OptimizationStep>,
}

#[instrument(err, skip(input, options), fields(segment_ids = ?input.segment_ids))]
pub async fn optimize_segments<'a>(
    input: OptimizeObjectInput<'a>,
    options: OptimizeObjectOptions,
) -> Result<OptimizeObjectOutput> {
    // Procedure:
    //
    // 1. Acquire segment information for the object (optionally scoped to the given segment_ids), and
    //    construct a list of when each segment was last processed, the compaction delta, and the
    //    number of rows in the segment.
    // 2. Compute the steps that would be performed.
    // 3. If dry_run, return the steps that would be performed.
    // 4. Otherwise, perform the steps. We try to perform only as many steps as there are indexing operations
    //    available. As soon as we hit enough rows, we stop.
    //
    // Returns the list of completed index operations and the list of steps that would be performed.

    let steps = construct_optimizable_segments(
        input.segment_ids,
        &input.config,
        OptimizationStepOptions {
            target_num_segments: options.merge_opts.target_num_segments as u64,
            recompact: input.recompact,
            ignore_old_segments: false,
        },
    )
    .await?;

    if input.dry_run {
        return Ok(OptimizeObjectOutput {
            performed: vec![],
            planned: steps,
        });
    }

    let planned = steps.clone();
    let output = async move {
        let (mut worker, mut completed_receiver) = OptimizationWorker::new(
            input.config.clone(),
            input.file_cache_opts,
            options.tuning_opts.clone(),
            options.merge_opts.clone(),
            options.compact_wal_opts.clone(),
            options.process_wal_opts.clone(),
        );

        let mut copied_steps = steps.clone();

        let collect_completed_steps = tokio::spawn(async move {
            let mut performed_steps = Vec::new();
            while let Some(step) = completed_receiver.recv().await {
                performed_steps.push(step);
            }
            performed_steps
        });

        for step in steps.into_iter() {
            worker
                .submit(RunnableOptimizationStep {
                    step,
                    schema: input.schema.clone(),
                })
                .await?;
        }

        worker.shutdown().await?;
        let performed_steps = collect_completed_steps.await?;

        copied_steps.retain(|step| !performed_steps.iter().any(|s| s.step == *step));

        Ok(OptimizeObjectOutput {
            performed: performed_steps,
            planned: copied_steps,
        })
    };

    if input.run_async {
        tokio::spawn(async move {
            let result = output.await;
            if let Err(e) = result {
                log::error!("Error optimizing object: {:?}", e);
            }
        });
        Ok(OptimizeObjectOutput {
            performed: vec![],
            planned,
        })
    } else {
        output.await
    }
}

pub struct OptimizableSegment {
    pub segment_id: Uuid,
    pub last_processed_xact_id: Option<TransactionId>,
    pub last_compacted_xact_id: Option<TransactionId>,
    pub num_tantivy_segments: u64,
    pub num_rows: u64,
    pub min_pagination_key: PaginationKey,
    pub has_stats: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct OptimizationStep {
    pub segment_id: Uuid,
    pub recompact: bool,
    pub compact: bool,
    pub merge: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformedOptimizationStep {
    #[serde(flatten)]
    pub step: OptimizationStep,
    pub num_rows_processed: u64,
    pub error: Option<String>,
    pub elapsed_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationStepOptions {
    pub target_num_segments: u64,
    pub recompact: bool,
    pub ignore_old_segments: bool,
}

impl Default for OptimizationStepOptions {
    fn default() -> Self {
        Self {
            target_num_segments: default_target_num_segments() as u64,
            recompact: false,
            ignore_old_segments: false,
        }
    }
}

async fn construct_optimizable_object(
    object_id: FullObjectId<'_>,
    config: &ConfigWithStore,
    options: OptimizationStepOptions,
) -> Result<Vec<OptimizationStep>> {
    let segment_ids = config
        .global_store
        .list_segment_ids(&[object_id], None)
        .await?
        .remove(0);

    let optimizable_segments =
        construct_optimizable_segments(&segment_ids, config, options).await?;

    Ok(optimizable_segments)
}

async fn construct_optimizable_segments(
    segment_ids: &[Uuid],
    config: &ConfigWithStore,
    options: OptimizationStepOptions,
) -> Result<Vec<OptimizationStep>> {
    let (segment_metas, segment_processed_xact_ids, latest_stats) = join!(
        config.global_store.query_segment_metadatas(segment_ids),
        config
            .global_store
            .query_segment_wal_entries_xact_id_statistic(
                segment_ids,
                SegmentWalEntriesXactIdStatistic::Max,
                None
            ),
        config
            .global_store
            .query_field_statistics(segment_ids, &[XACT_ID_FIELD]),
    );

    let segment_metas = segment_metas?;
    let segment_processed_xact_ids = segment_processed_xact_ids?;
    let latest_stats = latest_stats?;

    let optimizable_segments = segment_metas
        .iter()
        .enumerate()
        .map(|(i, meta)| OptimizableSegment {
            segment_id: segment_ids[i],
            last_processed_xact_id: segment_processed_xact_ids[i],
            last_compacted_xact_id: meta
                .last_compacted_index_meta
                .as_ref()
                .map(|meta| meta.xact_id),
            num_tantivy_segments: meta
                .last_compacted_index_meta
                .as_ref()
                .map(|meta| meta.tantivy_meta.segments.len() as u64)
                .unwrap_or(0),
            num_rows: meta.num_rows,
            min_pagination_key: meta.minimum_pagination_key,
            has_stats: latest_stats
                .get(&segment_ids[i])
                .and_then(|x| x.get(XACT_ID_FIELD))
                .is_some(),
        })
        .filter(|segment| {
            if options.ignore_old_segments {
                // Include only segments that were written to within the last 24 hours. If there is
                // no last_processed_xact_id, we shouldn't need to explicitly skip here because
                // there is no work to do.
                if let Some(last_processed_xact_id) = segment.last_processed_xact_id {
                    let cutoff = TransactionId::from_time_ago(
                        Duration::from_secs(OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS),
                        0,
                    );
                    let should_skip = last_processed_xact_id < cutoff;
                    if should_skip {
                        log::info!(
                            "[optimization_worker] Ignoring old segment {}",
                            segment.segment_id,
                        );
                    } else {
                        log::info!("[optimization_worker] Not ignoring recently-written segment {}", segment.segment_id);
                    }
                    return !should_skip;
                } else {
                    log::info!("[optimization_worker] Not ignoring segment {}, because it has no last_processed_xact_id", segment.segment_id);
                }
            }
            true
        })
        .collect();

    Ok(construct_optimization_steps(optimizable_segments, options))
}

pub fn construct_optimization_steps(
    mut optimizable_segments: Vec<OptimizableSegment>,
    options: OptimizationStepOptions,
) -> Vec<OptimizationStep> {
    let mut steps = Vec::new();

    // Prioritize the segments with the most segments to merge.
    optimizable_segments.sort_by_key(|s| (s.num_tantivy_segments as i64) * -1);

    // Compact the newest segments first (in descending order, for each segment whose last
    // processed xact_id is greater than the last compacted xact_id).
    for segment in optimizable_segments.iter() {
        if options.recompact {
            steps.push(OptimizationStep {
                segment_id: segment.segment_id,
                recompact: true,
                compact: true,
                merge: true,
            });
            continue;
        }

        let compact =
            (segment.last_processed_xact_id > segment.last_compacted_xact_id) || !segment.has_stats;
        let merge = segment.num_tantivy_segments > options.target_num_segments;

        if compact || merge {
            steps.push(OptimizationStep {
                segment_id: segment.segment_id,
                recompact: false,
                compact,
                merge,
            });
        }
    }

    steps
}

#[derive(Clone)]
pub struct OptimizeAllObjectsInput {
    pub config: ConfigWithStore,
    pub schema: Option<util::schema::Schema>,
    pub frontier: TransactionId,
    pub max_iterations: Option<u64>,
    pub storage_config: StorageConfig,
    pub file_cache_opts: FileCacheOpts,
    pub ignore_old_segment_object_ids: HashSet<FullObjectIdOwned>,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct OptimizeAllObjectsOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_opts: OptimizeObjectOptions,

    /// The maximum number of objects to process in a single optimization iteration.
    #[arg(
        long,
        default_value_t = 100,
        env = "BRAINSTORE_OPTIMIZE_ALL_OBJECTS_BATCH_SIZE"
    )]
    #[serde(default = "default_batch_size")]
    pub batch_size: u64,
}

fn default_batch_size() -> u64 {
    100
}

impl Default for OptimizeAllObjectsOptions {
    fn default() -> Self {
        Self {
            optimize_opts: Default::default(),
            batch_size: default_batch_size(),
        }
    }
}

pub struct OptimizationLoopOutput {
    pub frontier: TransactionId,
    // This number isn't a true "count distinct", because if an object is encountered twice,
    // it's counted twice. But it helps to get a sense of how many objects were processed.
    pub num_objects: u64,
}

pub async fn run_optimization_loop(
    input: OptimizeAllObjectsInput,
    options: OptimizeAllObjectsOptions,
) -> Result<OptimizationLoopOutput> {
    // This function implements an optimization loop that tries to optimize storage for all objects _up until_ the loop_state.frontier
    // (i.e. whose last_processed_xact_id >= input.frontier). Once it finishes a round of iteration, it tries again with an updated frontier,
    // and eventually once it runs out of iterations, it returns the frontier (or just sleeps and keeps going). Generally speaking, the frontier
    // should be a "long time ago", and by default it's set to a day before the current time.
    //
    // As it runs, it submits optimization jobs to the worker which runs in a separate task. Once a job is finished, we receive a message
    // on the receiver channel, which tells us how much work was done. If the step processed any rows, then we re-submit the object to the worker
    // to keep chewing on it until it's a no-op.

    let (mut worker, mut completed_receiver) = OptimizationWorker::new(
        input.config.clone(),
        input.file_cache_opts,
        options.optimize_opts.tuning_opts.clone(),
        options.optimize_opts.merge_opts.clone(),
        options.optimize_opts.compact_wal_opts.clone(),
        options.optimize_opts.process_wal_opts.clone(),
    );

    // This worker drains finished steps from the background receiver and just logs them. We don't currently do
    // anything with them.
    let background_receiver = tokio::spawn(async move {
        while let Some(step) = completed_receiver.recv().await {
            if step.num_rows_processed > 0 {
                log::info!("[run_optimization_loop] finished running step: {:?}", step);
            }
        }

        log::info!("[run_optimization_loop] background receiver closed");
        Ok::<_, util::anyhow::Error>(())
    });

    let mut iterations = 0;
    let mut result = OptimizationLoopOutput {
        frontier: input.frontier,
        num_objects: 0,
    };
    loop {
        // This inner loop works from the most recently written object "up to" the frontier, using the cursor to track
        // its progress along the way to the frontier.
        let mut cursor: Option<RecentObject> = None;
        let mut new_frontier = result.frontier.0;
        let mut object_iterations = 0;
        loop {
            let mut object_batch = input
                .config
                .global_store
                .query_recently_updated_objects(
                    cursor.as_ref().map(|c| c.cursor()),
                    options.batch_size as usize,
                )
                .await?;

            // Trim away any objects that are past the frontier we're supposed to optimize up to.
            object_batch.retain(|o| o.last_processed_xact_id >= result.frontier);

            if object_batch.is_empty() {
                break;
            }

            for object in object_batch {
                object_iterations += 1;
                let steps = construct_optimizable_object(
                    object.object_id.as_ref(),
                    &input.config,
                    OptimizationStepOptions {
                        target_num_segments: options.optimize_opts.merge_opts.target_num_segments
                            as u64,
                        recompact: false,
                        ignore_old_segments: {
                            let should_ignore = input
                                .ignore_old_segment_object_ids
                                .contains(&object.object_id);
                            if should_ignore {
                                log::info!(
                                    "[run_optimization_loop] ignoring old segments from object {}",
                                    object.object_id
                                );
                            }
                            should_ignore
                        },
                    },
                )
                .await?;

                let schema = match &input.schema {
                    Some(schema) => make_full_schema(schema)?,
                    None => make_full_schema(&make_object_schema(object.object_id.object_type)?)?,
                };

                if !steps.is_empty() {
                    log::info!(
                        "[run_optimization_loop] optimizing object: {:?}, steps: {:?}",
                        object,
                        steps
                    );
                }

                for step in steps {
                    worker
                        .submit(RunnableOptimizationStep {
                            step,
                            schema: schema.clone(),
                        })
                        .await?;
                }

                // The frontier is an inclusive lower bound on the last processed xact_id, so we want to
                // exclude this object's transaction (and therefore add 1).
                new_frontier = new_frontier.max(object.last_processed_xact_id.0 + 1);

                // Whereas the cursor helps us proceed within the current call to loop_iteration.
                cursor = match cursor {
                    Some(c) => Some(c.min(object)),
                    _ => Some(object),
                };
            }
        }

        result.frontier = TransactionId(new_frontier);
        result.num_objects += object_iterations;

        iterations += 1;
        if input
            .max_iterations
            .map(|max_iterations| iterations >= max_iterations)
            .unwrap_or(false)
        {
            log::info!("Reached max iterations ({}), stopping", iterations);
            break;
        }

        if object_iterations == 0 {
            log::debug!(
                "No objects to optimize, sleeping for 10 seconds (frontier: {}, cursor: {:?})",
                result.frontier,
                cursor
            );
            tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        }
    }

    // Wait for the background receiver to shut down.
    worker.shutdown().await?;

    // Wait for the background receiver task to shut down.
    background_receiver.await??;

    Ok(result)
}

struct OptimizationWorker {
    sender: Option<mpsc::Sender<RunnableOptimizationStep>>,
    consumer: tokio::task::JoinHandle<Result<()>>,
}

impl OptimizationWorker {
    pub fn new(
        config: ConfigWithStore,
        file_cache_opts: FileCacheOpts,
        tuning_opts: OptimizeObjectTuningOptions,
        merge_opts: MergeOpts,
        compact_opts: CompactSegmentWalOptions,
        process_wal_opts: ProcessObjectWalOptions,
    ) -> (Self, mpsc::Receiver<PerformedOptimizationStep>) {
        let max_concurrent_ops = global_limits().index_operations.capacity();

        // The limit on these channels is not strictly necessary but aligns with the concurrency limit enforced within the consumer.
        let (sender, receiver) = mpsc::channel(max_concurrent_ops);
        let (completed_sender, completed_receiver) = mpsc::channel(max_concurrent_ops);

        let consumer = OptimizationWorkerConsumer {
            receiver,
            opts: OptimizationWorkerOpts {
                completed: completed_sender,
                config,
                file_cache_opts,
                merge_page_size: tuning_opts.merge_page_size,
                merge_memory_limit: tuning_opts.merge_memory_limit,
                merge_opts,
                compact_opts,
                process_wal_opts,
            },
        };

        (
            Self {
                sender: Some(sender),
                consumer: consumer.start(),
            },
            completed_receiver,
        )
    }

    pub async fn submit(&mut self, step: RunnableOptimizationStep) -> Result<()> {
        self.sender.as_ref().unwrap().send(step).await?;
        Ok(())
    }

    pub async fn shutdown(mut self) -> Result<()> {
        drop(self.sender.take().unwrap());
        self.consumer.await??;
        Ok(())
    }
}

struct OptimizationWorkerConsumer {
    receiver: mpsc::Receiver<RunnableOptimizationStep>,
    opts: OptimizationWorkerOpts,
}

#[derive(Clone)]
struct OptimizationWorkerOpts {
    completed: mpsc::Sender<PerformedOptimizationStep>,

    config: ConfigWithStore,
    file_cache_opts: FileCacheOpts,
    merge_page_size: usize,
    merge_memory_limit: usize,

    merge_opts: MergeOpts,
    compact_opts: CompactSegmentWalOptions,
    process_wal_opts: ProcessObjectWalOptions,
}

impl OptimizationWorkerOpts {
    pub fn new_directory(&self, is_merge: bool) -> Result<ConfigWithStore> {
        let cache_opts = if is_merge {
            FileCacheOpts {
                page_size: self.merge_page_size,
                memory_limit: self.merge_memory_limit,
                ..self.file_cache_opts.clone()
            }
        } else {
            self.file_cache_opts.clone()
        };

        self.config.with_new_indexing_directory(cache_opts)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct RunnableOptimizationStep {
    step: OptimizationStep,
    schema: Schema,
}

const MAX_OPTIMIZATION_ITERATIONS: usize = 100;

impl OptimizationWorkerConsumer {
    fn start(mut self) -> tokio::task::JoinHandle<Result<()>> {
        tokio::spawn(async move {
            let mut set = tokio::task::JoinSet::new();

            loop {
                while set.len() >= self.receiver.max_capacity() {
                    if let Some(res) = set.join_next().await {
                        // This only fails when the task fails to join.
                        res.unwrap();
                    }
                }

                match self.receiver.recv().await {
                    Some(step) => {
                        set.spawn(OptimizationWorkerConsumer::run_step(
                            self.opts.clone(),
                            step,
                        ));
                    }
                    None => break,
                }
            }

            set.join_all().await;

            Ok(())
        })
    }

    async fn run_step(opts: OptimizationWorkerOpts, cmd: RunnableOptimizationStep) {
        let step = cmd.step.clone();
        let start_time = std::time::Instant::now();
        let processed_rows = Self::run_step_inner(&opts, cmd).await;
        let elapsed_time_ms = start_time.elapsed().as_millis() as u64;

        let step = match processed_rows {
            Ok(iter_processed_rows) => PerformedOptimizationStep {
                step,
                num_rows_processed: iter_processed_rows as u64,
                error: None,
                elapsed_time_ms,
            },
            Err(e) => PerformedOptimizationStep {
                step,
                num_rows_processed: 0,
                error: Some(e.to_string()),
                elapsed_time_ms,
            },
        };

        opts.completed.send(step).await.unwrap();
    }

    async fn run_step_inner(
        opts: &OptimizationWorkerOpts,
        cmd: RunnableOptimizationStep,
    ) -> Result<usize> {
        let mut iters = 0;
        let mut total_rows = 0;
        let mut should_recompact = cmd.step.recompact;
        while iters < MAX_OPTIMIZATION_ITERATIONS {
            let mut iter_processed_rows = 0;

            if should_recompact {
                match clear_compacted_index(ClearCompactedIndexInput {
                    segment_id: cmd.step.segment_id,
                    global_store: opts.config.global_store.clone(),
                    locks_manager: &*opts.config.locks_manager,
                })
                .await
                {
                    Ok(()) => {
                        should_recompact = false;
                    }
                    Err(e) => {
                        log::error!("Error clearing compacted index: {:?}", e);
                        return Err(e);
                    }
                };
            }

            if cmd.step.compact {
                let config = opts.new_directory(false)?;

                match compact_segment_wal(
                    CompactSegmentWalInput {
                        segment_id: cmd.step.segment_id,
                        index_store: config.index.clone(),
                        schema: cmd.schema.clone(),
                        global_store: config.global_store.clone(),
                        locks_manager: &*config.locks_manager,
                    },
                    CompactSegmentWalOptionalInput {
                        try_acquire: true,
                        use_status_updater: true,
                        ..Default::default()
                    },
                    opts.compact_opts.clone(),
                )
                .await
                {
                    Ok(r) => {
                        iter_processed_rows += r.num_wal_entries_compacted;
                    }
                    Err(e) => {
                        log::error!("Error compacting segment {}: {:?}", cmd.step.segment_id, e);
                        if should_recompact_invalid_segment(
                            cmd.step.segment_id,
                            &config.index,
                            opts.config.global_store.as_ref(),
                            &opts.compact_opts.writer_opts,
                        )
                        .await?
                        {
                            should_recompact = true;
                            continue;
                        }
                        return Err(e);
                    }
                };
            }

            if cmd.step.merge {
                let config = opts.new_directory(true)?;
                match merge_tantivy_segments(
                    MergeTantivySegmentsInput {
                        segment_id: cmd.step.segment_id,
                        config: config.clone(),
                        schema: &cmd.schema,
                        dry_run: false,
                        try_acquire: true,
                    },
                    MergeTantivySegmentsOptionalInput {
                        use_status_updater: true,
                        ..Default::default()
                    },
                    MergeTantivySegmentsOptions {
                        merge_opts: opts.merge_opts.clone(),
                        writer_opts: opts.compact_opts.writer_opts.clone(),
                        process_wal_opts: opts.process_wal_opts.clone(),
                    },
                )
                .await
                {
                    Ok(r) => {
                        iter_processed_rows += r.num_rows;
                    }
                    Err(e) => {
                        log::error!("Error merging segment: {:?}", e);
                        if should_recompact_invalid_segment(
                            cmd.step.segment_id,
                            &config.index,
                            opts.config.global_store.as_ref(),
                            &opts.compact_opts.writer_opts,
                        )
                        .await?
                        {
                            should_recompact = true;
                            continue;
                        }
                        return Err(e);
                    }
                };
            }

            if iter_processed_rows == 0 {
                // If we didn't process any rows, then we're done.
                break;
            }

            total_rows += iter_processed_rows;
            iters += 1;
        }

        if iters == MAX_OPTIMIZATION_ITERATIONS {
            log::warn!(
                "Reached max iterations ({}) for segment {} after processing {} rows, stopping",
                iters,
                cmd.step.segment_id,
                total_rows
            );
        }

        Ok(total_rows)
    }
}

async fn should_recompact_invalid_segment(
    segment_id: Uuid,
    index: &StoreInfo,
    global_store: &dyn GlobalStore,
    writer_opts: &TantivyIndexWriterOpts,
) -> Result<bool> {
    let segment_metas = global_store.query_segment_metadatas(&[segment_id]).await?;
    let segment_meta = segment_metas.first().unwrap();
    let index_meta = match &segment_meta.last_compacted_index_meta.as_ref() {
        Some(meta) => &meta.tantivy_meta,
        None => {
            return Err(anyhow!(
                "No compacted index metadata found for segment {}",
                segment_id
            ));
        }
    };
    let validation_result = validate_tantivy_index(
        index_meta.clone(),
        &index.store,
        &index.prefix,
        &TantivyIndexScope::Segment(segment_id),
        &writer_opts.validate_opts,
    )
    .await?;
    match validation_result.check_success() {
        Ok(validated_meta) => {
            log::info!(
                "Index validation succeeded for segment {}. last_compacted_index_meta: {:?}",
                segment_id,
                serde_json::to_string(&validated_meta)?
            );
            Ok(false)
        }
        Err(e) => {
            log::warn!(
                "Index validation failed for segment {}. {}. Will recompact.",
                segment_id,
                e
            );
            Ok(true)
        }
    }
}
