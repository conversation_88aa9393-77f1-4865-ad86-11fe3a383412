use std::{
    collections::{HashMap, HashSet},
    iter::Sum,
    ops::Add,
    path::PathBuf,
    sync::Arc,
};

use serde::{Deserialize, Deserializer, Serialize, Serializer};
use util::{
    anyhow::{anyhow, Result},
    async_trait::async_trait,
    chrono::{DateTime, Duration, TimeZone, Utc},
    functional::merge_options,
    system_types::{FullObjectId, FullObjectIdOwned, FullRowId},
    system_types::{FullRowIdOwned, ObjectType},
    tokio::sync::RwLock,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    directory::AsyncDirectoryArc,
    global_locks_manager::{GlobalLockWriteGuard, GlobalLocksManager},
    instrumented::{AsDynInstrumented, Instrumented},
    json_value_store::{read_json_value, write_json_value},
    tantivy_index::IndexMetaJson,
    vacuum_util::VacuumType,
};

#[derive(<PERSON><PERSON>, Debug, Default, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct SegmentLiveness {
    pub object_id: FullObjectIdOwned,
    pub is_live: bool,
}

#[derive(Clone, Debug)]
pub struct SegmentIdPaginationArgs {
    // If provided, return a paginated batch of ascending segment IDs starting past the input
    // segment ID cursor.
    pub segment_id_cursor: Option<Uuid>,
    pub limit: usize,
}

#[derive(Clone, Debug)]
pub struct ListSegmentIdsGlobalOptionalInput {
    pub pagination_args: Option<SegmentIdPaginationArgs>,
    // List non-live segments instead of live ones. This is useful when vacuuming dead segments.
    pub is_live: bool,
}

impl Default for ListSegmentIdsGlobalOptionalInput {
    fn default() -> Self {
        Self {
            pagination_args: None,
            is_live: true,
        }
    }
}

#[derive(Clone, Debug)]
pub struct ListSegmentIdsOptionalInput {
    // List non-live segments instead of live ones. This is useful when vacuuming dead segments.
    pub is_live: bool,
}

impl Default for ListSegmentIdsOptionalInput {
    fn default() -> Self {
        Self { is_live: true }
    }
}

#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct LastCompactedIndexMeta {
    /// The last transaction ID in the segment WAL which was compacted into tantivy.
    ///
    /// This value is somewhat of an approximation, because WAL processing is free to add entries
    /// with transaction ids <= this value afterwards. But we snapshot the value at the time of
    /// compaction so that we can quickly query it in places like the IndexWalReader without having
    /// to run GlobalStore::query_segment_wal_xact_id_statistic.
    pub xact_id: TransactionId,
    // Snapshot of the tantivy meta.json corresponding to the xact_id.
    pub tantivy_meta: IndexMetaJson,
}

#[derive(Clone, Debug, PartialEq, Serialize, Deserialize)]
pub enum LastIndexOperationDetails {
    #[serde(rename = "compact")]
    Compact { num_wal_entries: u64 },
    #[serde(rename = "merge")]
    Merge { merges: Vec<Vec<String>> },
}

#[derive(Clone, Debug, PartialEq, Eq, Serialize, Deserialize)]
pub struct SegmentFieldStatistics {
    min: u64,
    max: u64,
}

impl SegmentFieldStatistics {
    pub fn new(min: u64, max: u64) -> Result<Self> {
        if min > max {
            return Err(anyhow!("min must be <= max"));
        };
        Ok(Self { min, max })
    }

    pub fn min(&self) -> u64 {
        self.min
    }

    pub fn max(&self) -> u64 {
        self.max
    }

    pub fn merge(
        lhs: SegmentFieldStatistics,
        rhs: SegmentFieldStatistics,
    ) -> SegmentFieldStatistics {
        SegmentFieldStatistics {
            min: std::cmp::min(lhs.min, rhs.min),
            max: std::cmp::max(lhs.max, rhs.max),
        }
    }
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct ObjectMetadata {
    /// The last transaction ID that was processed from the object WAL into segment WALs for this
    /// object.
    pub last_processed_xact_id: Option<TransactionId>,
    /// A unique token for namespacing the object's WAL entries. This can be reset to logically
    /// delete the WAL entries for an object.
    pub wal_token: Uuid,
}

// PartialOrd automatically uses the lexicographic ordering of the fields in the struct
#[derive(Clone, Debug, Default, PartialEq, Eq, PartialOrd, Ord)]
pub struct RecentObjectCursor<'a> {
    pub last_processed_xact_id: TransactionId,
    pub object_id: FullObjectId<'a>,
}

// PartialOrd automatically uses the lexicographic ordering of the fields in the struct
#[derive(Clone, Debug, Default, PartialEq, Eq, PartialOrd, Ord)]
pub struct RecentObject {
    pub last_processed_xact_id: TransactionId,
    pub object_id: FullObjectIdOwned,
}

impl RecentObject {
    pub fn cursor(&self) -> RecentObjectCursor<'_> {
        RecentObjectCursor {
            last_processed_xact_id: self.last_processed_xact_id,
            object_id: self.object_id.as_ref(),
        }
    }
}

#[derive(Clone, Debug, Default)]
pub struct ObjectMetadataUpdate {
    pub last_processed_xact_id: Option<(Option<TransactionId>, Option<TransactionId>)>,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct SegmentMetadata {
    /// Metadata about the last compaction of the segment.
    pub last_compacted_index_meta: Option<LastCompactedIndexMeta>,
    /// The earliest pagination key written to the segment WAL.
    pub minimum_pagination_key: PaginationKey,
    /// The number of unique row IDs stored in the segment.
    ///
    /// Note: if we can ensure it's fast to compute this using the RowIdRootSpanIdSegmentMembership
    /// info (which is going to be a table keyed by `segment_id` in postgres), then we could ditch
    /// this redundant counter. Otherwise we have to ensure this stays in sync with that table.
    pub num_rows: u64,
}

#[derive(Clone, Debug, Default)]
pub struct SegmentMetadataUpdate {
    pub last_compacted_index_meta: Option<(
        Option<LastCompactedIndexMeta>,
        Option<LastCompactedIndexMeta>,
    )>,
    pub minimum_pagination_key: Option<(PaginationKey, PaginationKey)>,
    pub add_num_rows: Option<u64>,
}

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct SegmentWalEntry {
    pub xact_id: TransactionId,
    pub wal_filename: Uuid,
    pub byte_range_start: usize,
    pub byte_range_end: usize,
    pub is_compacted: bool,
    pub digest: Option<i64>,
    pub deleted_at: Option<DateTime<Utc>>,
}

#[derive(Clone, Debug, Default)]
pub struct UpsertSegmentWalEntry {
    pub xact_id: TransactionId,
    pub byte_range_start: usize,
    pub byte_range_end: usize,
    pub is_compacted: bool,
    pub digest: Option<i64>,
}

#[derive(Clone, Debug, PartialEq, Eq)]
pub enum SegmentWalEntriesCursor {
    XactIdGe(TransactionId),
    XactIdWalFilenameGt {
        xact_id: TransactionId,
        wal_filename: Uuid,
    },
}

#[derive(Clone, Debug)]
pub enum SegmentWalEntriesXactIdStatistic {
    Min,
    Max,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct LastIndexOperation {
    pub finished: Option<bool>,
    pub estimated_progress: Option<f64>,
    pub stage: Option<String>,
    pub error: Option<String>,
    pub details: Option<LastIndexOperationDetails>,
}

#[derive(Clone, Debug, Default, PartialEq, Serialize, Deserialize)]
pub struct LastIndexOperationResult {
    pub start: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
    #[serde(flatten)]
    pub operation: LastIndexOperation,
    pub current_op_token: Option<Uuid>,
}

#[derive(Clone, Copy, Debug, Default)]
pub struct LastIndexOpTokenOpts {
    // Apply the update regardless of the current operation token.
    pub always_update: bool,
    // If the update applies, instead of setting the current_op_token to the given one, clear it
    // out.
    pub clear_value: bool,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct SegmentVacuumState {
    pub last_written_ts: DateTime<Utc>,
    pub vacuum_index_last_successful_start_ts: DateTime<Utc>,
}

impl Default for SegmentVacuumState {
    fn default() -> Self {
        Self {
            last_written_ts: Utc.with_ymd_and_hms(1980, 1, 1, 0, 0, 0).unwrap(),
            vacuum_index_last_successful_start_ts: Utc
                .with_ymd_and_hms(1970, 1, 1, 0, 0, 0)
                .unwrap(),
        }
    }
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct SegmentLivenessAndVacuumState {
    liveness: SegmentLiveness,
    vacuum_state: SegmentVacuumState,
}

pub struct NotifyCommitSender {
    sender: tokio::sync::oneshot::Sender<bool>,
}

impl NotifyCommitSender {
    pub fn notify(self, should_commit: bool) {
        match self.sender.send(should_commit) {
            Ok(_) => (),
            Err(e) => {
                log::warn!("Failed to send commit notification to global store operation (likely aborted early): {:?}", e);
            }
        }
    }
}

pub struct NotifyCommitReceiver {
    receiver: tokio::sync::oneshot::Receiver<bool>,
}

impl NotifyCommitReceiver {
    pub async fn wait(self) -> bool {
        match self.receiver.await {
            Ok(should_commit) => should_commit,
            Err(e) => {
                log::warn!("Failed to receive commit notification from global store operation (sender likely aborted early). Rolling back: {:?}", e);
                false
            }
        }
    }
}

pub fn make_notify_commit_channel() -> (NotifyCommitSender, NotifyCommitReceiver) {
    let (sender, receiver) = tokio::sync::oneshot::channel();
    (
        NotifyCommitSender { sender },
        NotifyCommitReceiver { receiver },
    )
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct TimeBasedRetentionState {
    pub last_successful_start_ts: Option<DateTime<Utc>>,
    pub current_op_start_ts: Option<DateTime<Utc>>,
    // Cursor is either None or an (object_id, segment_id) pair.
    pub cursor: Option<TimeBasedRetentionCursor>,
    pub operation: TimeBasedRetentionOperation,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct TimeBasedRetentionCursor {
    pub object_id: FullObjectIdOwned,
    pub segment_id: Uuid,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct TimeBasedRetentionOperation {
    pub error: Option<String>,
    pub completed_ts: Option<DateTime<Utc>>,
    #[serde(flatten)]
    #[serde(default)]
    pub stats: TimeBasedRetentionStats,
}

#[derive(Clone, Copy, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct TimeBasedRetentionStats {
    #[serde(default)]
    pub num_processed_objects: u64,
    #[serde(default)]
    pub num_processed_segments: u64,
    #[serde(flatten)]
    #[serde(default)]
    pub segment_stats: DeleteFromSegmentStats,
}

impl Add for TimeBasedRetentionStats {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self {
            num_processed_objects: self.num_processed_objects + other.num_processed_objects,
            num_processed_segments: self.num_processed_segments + other.num_processed_segments,
            segment_stats: self.segment_stats + other.segment_stats,
        }
    }
}

#[derive(Clone, Copy, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct DeleteFromSegmentStats {
    #[serde(flatten)]
    #[serde(default)]
    pub wal_stats: DeleteFromSegmentWalStats,
    #[serde(flatten)]
    #[serde(default)]
    pub index_stats: DeleteFromSegmentIndexStats,
}

impl Add for DeleteFromSegmentStats {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self {
            wal_stats: self.wal_stats + other.wal_stats,
            index_stats: self.index_stats + other.index_stats,
        }
    }
}

impl Sum for DeleteFromSegmentStats {
    fn sum<I: Iterator<Item = Self>>(iter: I) -> Self {
        iter.fold(Self::default(), Add::add)
    }
}

#[derive(Clone, Copy, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct DeleteFromSegmentWalStats {
    #[serde(default)]
    pub planned_num_deleted_wal_entries: u64,
    #[serde(default)]
    pub num_deleted_wal_entries: u64,
}

impl Add for DeleteFromSegmentWalStats {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self {
            planned_num_deleted_wal_entries: self.planned_num_deleted_wal_entries
                + other.planned_num_deleted_wal_entries,
            num_deleted_wal_entries: self.num_deleted_wal_entries + other.num_deleted_wal_entries,
        }
    }
}

impl Sum for DeleteFromSegmentWalStats {
    fn sum<I: Iterator<Item = Self>>(iter: I) -> Self {
        iter.fold(Self::default(), Add::add)
    }
}

#[derive(Clone, Copy, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct DeleteFromSegmentIndexStats {
    #[serde(default)]
    pub planned_num_deleted_index_docs: u64,
    #[serde(default)]
    pub num_deleted_index_docs: u64,
    #[serde(default)]
    pub num_write_locks: u64,
}

impl Add for DeleteFromSegmentIndexStats {
    type Output = Self;

    fn add(self, other: Self) -> Self {
        Self {
            planned_num_deleted_index_docs: self.planned_num_deleted_index_docs
                + other.planned_num_deleted_index_docs,
            num_deleted_index_docs: self.num_deleted_index_docs + other.num_deleted_index_docs,
            num_write_locks: self.num_write_locks + other.num_write_locks,
        }
    }
}

impl Sum for DeleteFromSegmentIndexStats {
    fn sum<I: Iterator<Item = Self>>(iter: I) -> Self {
        iter.fold(Self::default(), Add::add)
    }
}
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct BackfillTrackingEntryId<'a> {
    pub project_id: &'a str,
    pub object_type: ObjectType,
}

impl std::fmt::Display for BackfillTrackingEntryId<'_> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.object_type, self.project_id)
    }
}

#[derive(Default, Clone, Debug, Serialize, Deserialize)]
pub struct BackfillTrackingEntry {
    // These fields mirror those in the `brainstore_backfill_tracked_objects`
    // table in postgres.
    pub project_id: String,
    pub object_type: ObjectType,
    pub last_processed_sequence_id: i64,
    pub last_encountered_sequence_id: i64,
    pub last_processed_sequence_id_2: i64,
    pub last_encountered_sequence_id_2: i64,
    pub completed_initial_backfill_ts: Option<DateTime<Utc>>,
}

impl BackfillTrackingEntry {
    pub fn id(&self) -> BackfillTrackingEntryId {
        BackfillTrackingEntryId {
            project_id: &self.project_id,
            object_type: self.object_type,
        }
    }
}

#[derive(Default, Clone, Debug, Serialize, Deserialize)]
pub struct BackfillTrackingEntryUpdate {
    // These fields are maxed with the current value of the field in the update.
    pub last_processed_sequence_id: Option<i64>,
    pub last_processed_sequence_id_2: Option<i64>,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct BackfillBrainstoreObject {
    pub project_id: String,
    pub object_id: FullObjectIdOwned,
    pub is_logs2: bool,
    pub min_sequence_id: i64,
    pub max_sequence_id: i64,
    pub min_xact_id: TransactionId,
    pub max_xact_id: TransactionId,
}

// Used to insert/query test data.
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TestingOnlyBackfillBrainstoreObjectAtom {
    pub project_id: String,
    pub object_id: FullObjectIdOwned,
    pub is_logs2: bool,
    pub sequence_id: i64,
    pub xact_id: TransactionId,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum IdSegmentMembershipType {
    RowId,
    RootSpanId,
}

impl std::fmt::Display for IdSegmentMembershipType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            IdSegmentMembershipType::RowId => write!(f, "row_id"),
            IdSegmentMembershipType::RootSpanId => write!(f, "root_span_id"),
        }
    }
}

pub enum TaskType {
    TimeBasedRetention,
    // TODO: Add other task types (VacuumIndex, VacuumSegmentWAL, etc.) here.
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum TaskInfo {
    TimeBasedRetention(TimeBasedRetentionInfo),
    VacuumIndex(VacuumIndexInfo),
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct TaskInfos {
    pub time_based_retention: Option<TimeBasedRetentionInfo>,
    pub vacuum_index: Option<VacuumIndexInfo>,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize, PartialEq, Eq)]
pub struct TimeBasedRetentionInfo {
    pub start_ts: DateTime<Utc>,
    pub completed_ts: Option<DateTime<Utc>>,
    pub retention_days: i64,
    pub min_retained_xact_id: TransactionId,
    pub error: Option<String>,
    pub batch_stats: Option<DeleteFromSegmentStats>,
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
pub struct VacuumIndexInfo {
    pub error: Option<serde_json::Value>,
    pub start_ts: Option<DateTime<Utc>>,
    pub completed_ts: Option<DateTime<Utc>>,
    pub num_deleted_files_batch: Option<usize>,
}

/// A GlobalStore provides a variety of operations for reading/writing metadata at the global
/// context. It is generally used before operations can narrow-down to a specific segment, at which
/// point they can use the corresponding SegmentStore.
///
/// All mutation operations are atomic, meaning if the operation fails, the data will not have
/// changed. Across concurrent mutation operations, the store is responsible for handling
/// synchronization.
#[async_trait]
pub trait GlobalStore: std::fmt::Debug + Send + Sync + AsDynInstrumented {
    // Methods for managing segment IDs across objects.

    /// List all live segment IDs in the global store.
    async fn list_segment_ids_global(
        &self,
        optional_input: Option<ListSegmentIdsGlobalOptionalInput>,
    ) -> Result<Vec<Uuid>>;

    /// List all segment IDs associated with the given objects.
    async fn list_segment_ids(
        &self,
        object_ids: &[FullObjectId],
        optional_input: Option<ListSegmentIdsOptionalInput>,
    ) -> Result<Vec<Vec<Uuid>>>;

    /// Get the object IDs and liveness info associated with each given segment. Errors out if a
    /// segment does not exist.
    async fn query_segment_liveness(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentLiveness>>;

    /// Add/remove segment IDs associated with the given objects. Added segment IDs must be
    /// globally unique. Once an ID is removed, it cannot be re-used.
    async fn update_segment_ids(
        &self,
        object_id_add_remove_segment_ids: &[(FullObjectId, &[Uuid], &[Uuid])],
    ) -> Result<()>;

    /// Purge the given segment ID entries from the store. None of the segments may be live.
    async fn purge_segment_ids(&self, segment_ids: &[Uuid]) -> Result<()>;

    /// Get the SegmentMetadata associated with each given segment. Errors out if a given segment
    /// does not have any metadata.
    async fn query_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentMetadata>>;

    /// Apply the given segment metadata updates as CAS updates. If the segment does not exist, it
    /// is default-initialized and then the update is applied. Also sets `last_written` for the
    /// segments to the current timestamp. Returns true if the updates succeeded, false if they
    /// were aborted.
    async fn upsert_segment_metadatas(
        &self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
    ) -> Result<bool>;

    /// Purge segment metadatas for the given segments. None of the segments may be live.
    async fn purge_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<()>;

    // Methods for managing per-object metadata.

    /// Retrieve the ObjectMetadata associated with each given object. For objects with no known
    /// metadata, we fill in a default.
    async fn query_object_metadatas(
        &self,
        object_ids: &[FullObjectId],
    ) -> Result<Vec<ObjectMetadata>>;

    /// Get several object IDs that have been processed before max_compacted_xact_id (if specified)
    /// sorted by the last compacted xact_id (most recent first).
    async fn query_recently_updated_objects<'a>(
        &self,
        max_compacted_xact_id: Option<RecentObjectCursor<'a>>,
        limit: usize,
    ) -> Result<Vec<RecentObject>>;

    /// Apply the given object metadata updates as CAS updates. If the segment does not exist, it
    /// is default-initialized and then the update is applied. Returns true if the updates
    /// succeeded, false if they were aborted.
    async fn upsert_object_metadatas(
        &self,
        updates: HashMap<FullObjectId<'_>, ObjectMetadataUpdate>,
    ) -> Result<bool>;

    /// Purge all metadata for this object.
    async fn purge_object_metadatas(&self, object_ids: &[FullObjectId]) -> Result<()>;

    // Methods for managing per-segment metadata.

    /// Given a list of segments and a list of row_ids/root_span_ids over a
    /// single object ID, return a mapping from row_id/root_span_id ->
    /// segment_id (within the given list) containing it. Returns an error if
    /// there is any inconsistency between which segment a particular id belongs
    /// to within the given list of segments.
    async fn query_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_ids: &[Uuid],
        ids: &[FullRowId<'_, '_>],
    ) -> Result<HashMap<FullRowIdOwned, Uuid>>;

    /// Add the given row ids under each given segment id. We do not check that the row ids are
    /// unique to each segment, as there may be times where this is untrue, e.g. when merging
    /// segments. The caller is expected to handle this by atomically updating the list of live
    /// segments when the data is fully-populated.
    ///
    /// Errors if any of the id -> segment mappings already exist, so that if it succeeds, all
    /// mappings were added, and if it fails, none will be added.
    async fn add_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_id_to_entries: HashMap<Uuid, Vec<FullRowId<'_, '_>>>,
    ) -> Result<()>;

    /// Purge all id membership info for the given segments. This is intended for
    /// purging / space reclamation.
    async fn purge_id_segment_membership(&self, segment_ids: &[Uuid]) -> Result<()>;

    // Copy all id-segment membership info from the given source segments over to the destination
    // segment. This is intended for batch operations like merging segments, where running
    // individual query/add operations would be too memory-intensive. Re-copying is not an error.
    async fn copy_id_segment_membership(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()>;

    // Methods for managing per-segment wal entry metadata.

    /// Query for a batch of WAL entries for the given segment. The query accepts an optional
    /// cursor which can be used to paginate through the entries and an optional limit (only rows
    /// greater than the cursor value are returned).
    ///
    /// Returns the batch of WAL entries in order of (xact_id, wal_filename). If the result set is
    /// smaller than the provided limit, the caller can infer we have reached the end of the result
    /// set (excluding concurrent insertions).
    async fn query_segment_wal_entries_batch(
        &self,
        segment_id: Uuid,
        cursor: Option<SegmentWalEntriesCursor>,
        limit: Option<i64>,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<SegmentWalEntry>>;

    /// Query for the existence of any WAL entries for the given set of segments. Returns a boolean
    /// aligned with each segment indicating whether any WAL entries exist for that segment.
    async fn query_segment_wal_entries_existence(
        &self,
        segment_id_cursors: &[(Uuid, Option<SegmentWalEntriesCursor>)],
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<bool>>;

    /// Query a statistic over the xact_id values of the WAL entries for the given segments.
    async fn query_segment_wal_entries_xact_id_statistic(
        &self,
        segment_ids: &[Uuid],
        statistic: SegmentWalEntriesXactIdStatistic,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<Option<TransactionId>>>;

    /// Upsert the given WAL entries for each segment (first key) and WAL filename (second key).
    /// For any existing exact matches on (segment_id, xact_id, wal_filename), only the
    /// is_compacted boolean is adjusted to the new value. Otherwise, if the digest already
    /// matches an existing entry for the same (segment_id, xact_id) on a different wal_filename,
    /// the entry is ignored.
    ///
    /// Returns the number of entries which were not ignored due to digest matches.
    async fn upsert_segment_wal_entries(
        &self,
        segment_id_to_wal_filename_to_entries: HashMap<
            Uuid,
            HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        >,
    ) -> Result<u64>;

    /// Update the is_compacted value for the given WAL entries. Fails if any of
    /// the entries do not exist. This query is marked non-atomic, because in
    /// practice it is okay if it fails partway through.
    async fn update_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_id_xact_id_wal_filenames: &[(Uuid, TransactionId, Uuid)],
        is_compacted: bool,
    ) -> Result<()>;

    // Same as update_segment_wal_entries_is_compacted_non_atomic, except it
    // updates all entries in the given segments. This query is marked
    // non-atomic, because in practice it is okay if it fails partway through.
    async fn update_all_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_ids: &[Uuid],
        is_compacted: bool,
    ) -> Result<()>;

    /// Purge all WAL entries for the given segments. This is intended for purging / space
    /// reclamation.
    async fn purge_segment_wal_entries(&self, segment_ids: &[Uuid]) -> Result<()>;

    // Copy all WAL entry info from the given source segments over to the destination segment. This
    // is intended for batch operations like merging segments, where running individual query/add
    // operations would be too memory-intensive.
    //
    // Similar to upsert, duplicates are resolved by updating just the is_compacted boolean.
    async fn copy_segment_wal_entries(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()>;

    /// Get the last index operation for each given segment. If none exists, will return None.
    async fn query_last_index_operations(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<LastIndexOperationResult>>>;

    /// Upsert the given last index operation. If there is an existing entry, compares the
    /// `current_op_token` with the given `op_token`, and updates according to
    /// `UpsertLastIndexOpTokenOpts`.
    ///
    /// The `start` field will be updated if the update is applied and the op token differs from
    /// what was previously stored.
    async fn upsert_last_index_operation(
        &self,
        segment_id: Uuid,
        operation: LastIndexOperation,
        op_token: Uuid,
        op_token_opts: LastIndexOpTokenOpts,
    ) -> Result<()>;

    /// Bump the `last_updated` timestamp for the current last index operation if its
    /// `current_op_token` matches the one provided.
    async fn bump_last_index_operation_updated_ts(
        &self,
        segment_id: Uuid,
        op_token: Uuid,
    ) -> Result<()>;

    // Query field statistics for any entries matching the given segment_ids AND field_names.
    // Returns a mapping of segment_id -> field_name -> statistics for all matching entries.
    async fn query_field_statistics(
        &self,
        segment_ids: &[Uuid],
        field_names: &[&str],
    ) -> Result<HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>>;

    // Upsert the given set of field statistics.
    async fn upsert_field_statistics(
        &self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
    ) -> Result<()>;

    // Atomically upsert segment metadatas and field statistics, and set `last_written` for the
    // segments to the current timestamp. If any of the updates fail, the entire update is aborted.
    async fn upsert_segment_metadatas_and_field_statistics(
        &self,
        updates: HashMap<Uuid, (SegmentMetadataUpdate, Vec<(&str, SegmentFieldStatistics)>)>,
    ) -> Result<bool>;

    // Implementations of common operations.
    async fn query_segment_wal_min_uncompacted_xact_id(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<TransactionId>>> {
        self.query_segment_wal_entries_xact_id_statistic(
            segment_ids,
            SegmentWalEntriesXactIdStatistic::Min,
            Some(false),
        )
        .await
    }

    async fn query_segment_wal_max_compacted_xact_id(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<TransactionId>>> {
        self.query_segment_wal_entries_xact_id_statistic(
            segment_ids,
            SegmentWalEntriesXactIdStatistic::Max,
            Some(true),
        )
        .await
    }

    /// Returns the set of WAL filenames whose entries have all been fully purged
    /// (i.e. there are no live or soft-deleted entries remaining).
    async fn query_purged_wal_filenames(
        &self,
        segment_id: Uuid,
        wal_filenames: &[Uuid],
    ) -> Result<Vec<Uuid>>;

    /// Count WAL entries up to xact_id (exclusive) for the provided segments.
    async fn count_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64>;

    /// Mark WAL entries as deleted if they are older than the input xact_id.
    /// This is a soft delete; call `purge_deleted_segment_wal_entries` to fully purge
    /// the WAL entries, or `restore_segment_wal_entries_up_to_xact_id` to restore them.
    async fn delete_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64>;

    /// Purge WAL entries that were deleted more than `expiration_seconds` ago.
    async fn purge_deleted_segment_wal_entries(
        &self,
        segment_ids: &[Uuid],
        expiration_seconds: u64,
    ) -> Result<u64>;

    /// Restore soft-deleted WAL entries up to the specified xact_id threshold.
    /// This sets `deleted_at = NULL` for entries that were previously soft-deleted
    /// and have xact_id < the specified threshold (exclusive upper bound).
    /// Returns the number of entries restored.
    async fn restore_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64>;

    /// Retrieve segments eligible for index vacuum. Segments are considered vacuumable if:
    ///  (1) never previously vacuumed, or
    ///  (2) the last successful vacuum occurred more than `vacuum_period_seconds` before the current
    ///      vacuum run's `start_ts` and at least `max_last_successful_start_minus_last_written_seconds`
    ///      before the last write to the segment.
    async fn query_vacuum_index_segment_ids(
        &self,
        limit: usize,
        max_last_successful_start_minus_last_written_seconds: i64,
        vacuum_period_seconds: i64,
        start_ts: DateTime<Utc>,
        object_ids: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<Uuid>>;

    /// Retrieve vacuum states for the provided segments.
    async fn query_segment_vacuum_state(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, SegmentVacuumState>>;

    /// Upsert last_successful_start_ts for the given vacuum type for the provided segments.
    async fn upsert_segment_vacuum_last_successful_start_ts(
        &self,
        segment_ids: &[Uuid],
        vacuum_type: VacuumType,
        last_successful_start_ts: DateTime<Utc>,
    ) -> Result<()>;

    /// Upsert a last-segment-write timestamp for the specified segments.
    /// Defaults to the current timestamp if None is provided.
    async fn upsert_segment_last_written_ts(
        &self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
    ) -> Result<()>;

    /// List object IDs in ascending order with the given limit, starting past
    /// `object_id_cursor` and filtering by `object_ids_filter` if provided.
    async fn list_object_ids(
        &self,
        object_id_cursor: Option<FullObjectId<'_>>,
        limit: usize,
        object_ids_filter: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<FullObjectIdOwned>>;

    /// List an object's live segment IDs in ascending order with the given
    /// limit, starting past segment_id_cursor if provided.
    async fn list_object_segment_ids(
        &self,
        object_id: FullObjectId<'_>,
        segment_id_cursor: Uuid,
        limit: usize,
    ) -> Result<Vec<Uuid>>;

    /// List live (object_id, segment_id) pairs in ascending order for the given objects.
    async fn list_object_ids_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        limit: usize,
    ) -> Result<Vec<(FullObjectIdOwned, Uuid)>>;

    /// Query time-based retention state.
    async fn query_time_based_retention_state(&self) -> Result<TimeBasedRetentionState>;

    /// Upsert time-based retention state.
    async fn upsert_time_based_retention_state(
        &self,
        state: &TimeBasedRetentionState,
    ) -> Result<()>;

    /// Query info about all retention and vacuum runs for the given segments.
    async fn query_segment_task_infos(&self, segment_ids: &[Uuid]) -> Result<Vec<TaskInfos>>;

    /// Upsert info about a vacuum or retention run for the given segments.
    async fn upsert_segment_task_info(&self, segment_ids: &[Uuid], info: &TaskInfo) -> Result<()>;

    /// Fetch a batch of backfill tracking entries that are not yet fully backfilled. Explicitly
    /// specify whether the entries have completed backfilling (realtime backfill) or have not
    /// (historical backfill). Optionally provide a cursor for pagination (will return entries
    /// strictly greater than the cursor).
    async fn query_unbackfilled_tracking_entries_ordered(
        &self,
        has_completed_initial_backfill: bool,
        cursor: Option<BackfillTrackingEntryId<'_>>,
        limit: usize,
    ) -> Result<Vec<BackfillTrackingEntry>>;

    /// Fetch the full tracking entries for the specified IDs. Returns None for
    /// entries that do not exist.
    async fn query_backfill_tracking_entries_by_ids(
        &self,
        entries: &[BackfillTrackingEntryId<'_>],
    ) -> Result<Vec<Option<BackfillTrackingEntry>>>;

    /// Update a set of backfill tracking entries. Will error if any of the
    /// entries do not exist. Returns the updated entries.
    async fn update_backfill_tracking_entries(
        &self,
        updates: Vec<(BackfillTrackingEntryId<'_>, BackfillTrackingEntryUpdate)>,
    ) -> Result<Vec<BackfillTrackingEntry>>;

    /// Fetch the set of brainstore objects corresponding to the given set of
    /// tracking entry ids, within the given sequence ID range, inclusive.
    ///
    /// Returns a nested list of objects, where each outer list aligns with the
    /// input list of tracking entries.
    async fn query_backfill_brainstore_objects(
        &self,
        tracking_entries: &[BackfillTrackingEntryId<'_>],
        is_logs2: bool,
        min_sequence_id: i64,
        max_sequence_id: i64,
    ) -> Result<Vec<Vec<BackfillBrainstoreObject>>>;

    /// For testing only, insert a set of tracking entries and backfill object
    /// atoms into the global store. Will error if any duplicates are
    /// encountered.
    #[cfg(test)]
    async fn testing_only_insert_backfill_data(
        &self,
        tracking_entries: Vec<BackfillTrackingEntry>,
        brainstore_objects: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
    ) -> Result<()>;

    /// Health status.
    async fn status(&self) -> Result<String>;
}

// Derive a cursor for the next WAL entry query based on the given output.
pub fn compute_next_wal_entry_cursor(
    entries: &[SegmentWalEntry],
) -> Option<SegmentWalEntriesCursor> {
    entries
        .last()
        .map(|entry| SegmentWalEntriesCursor::XactIdWalFilenameGt {
            xact_id: entry.xact_id,
            wal_filename: entry.wal_filename,
        })
}

// In-memory global store.
#[derive(Debug, Default)]
pub struct MemoryGlobalStore {
    data: RwLock<MemoryGlobalStoreData>,
}

fn serialize_uuid_map<S, T>(map: &HashMap<Uuid, T>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
    T: Serialize,
{
    let seq = map
        .iter()
        .map(|(k, v)| (k.to_string(), v))
        .collect::<Vec<(String, &T)>>();
    seq.serialize(serializer)
}

pub fn deserialize_uuid_map<'de, D, T>(deserializer: D) -> Result<HashMap<Uuid, T>, D::Error>
where
    D: Deserializer<'de>,
    T: Deserialize<'de>,
{
    let seq = Vec::<(String, T)>::deserialize(deserializer)?;
    Ok(seq
        .into_iter()
        .map(|(k, v)| (k.parse().unwrap(), v))
        .collect())
}

#[derive(Clone, Debug, Default, Serialize, Deserialize)]
struct MemoryGlobalStoreData {
    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_liveness_and_vacuum_state: HashMap<Uuid, SegmentLivenessAndVacuumState>,
    pub object_id_to_metadata: HashMap<FullObjectIdOwned, ObjectMetadata>,
    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_metadata: HashMap<Uuid, SegmentMetadata>,
    pub row_id_to_segment_id: HashMap<String, HashSet<Uuid>>,
    pub root_span_id_to_segment_id: HashMap<String, HashSet<Uuid>>,
    // The map keys are [segment_id, xact_id, wal_filename]. Together, they correspond to the
    // unique key for a WAL entry.
    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_wal_entries: HashMap<Uuid, HashMap<String, HashMap<String, SegmentWalEntry>>>,
    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_last_index_operation: HashMap<Uuid, LastIndexOperationResult>,
    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_last_index_operation_details: HashMap<Uuid, LastIndexOperationDetails>,

    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_field_name_to_statistics:
        HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>,

    pub time_based_retention_state: TimeBasedRetentionState,

    #[serde(
        serialize_with = "serialize_uuid_map",
        deserialize_with = "deserialize_uuid_map"
    )]
    pub segment_id_to_task_infos: HashMap<Uuid, TaskInfos>,

    // project_id -> ObjectType -> tracking entry
    #[serde(default)]
    pub backfill_tracking_entries: HashMap<String, HashMap<ObjectType, BackfillTrackingEntry>>,
    #[serde(default)]
    pub backfill_brainstore_objects: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
}

impl MemoryGlobalStoreData {
    fn list_segment_ids_global(
        &self,
        optional_input: Option<ListSegmentIdsGlobalOptionalInput>,
    ) -> Result<Vec<Uuid>> {
        let optional_input = optional_input.unwrap_or_default();

        let live_segment_ids = self
            .segment_id_to_liveness_and_vacuum_state
            .iter()
            .filter(|(_, info)| info.liveness.is_live == optional_input.is_live)
            .map(|(key, _)| *key)
            .collect::<Vec<Uuid>>();

        if let Some(pagination_args) = optional_input.pagination_args {
            let mut sorted_segment_ids = live_segment_ids;
            sorted_segment_ids.sort();

            let start_idx = pagination_args.segment_id_cursor.map_or(0, |id| {
                sorted_segment_ids
                    .binary_search(&id)
                    .map_or_else(|err| err, |idx| idx + 1)
            });

            if start_idx >= sorted_segment_ids.len() {
                return Ok(vec![]);
            }

            Ok(sorted_segment_ids[start_idx
                ..sorted_segment_ids
                    .len()
                    .min(start_idx + pagination_args.limit)]
                .to_vec())
        } else {
            Ok(live_segment_ids)
        }
    }

    fn list_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        optional_input: Option<ListSegmentIdsOptionalInput>,
    ) -> Result<Vec<Vec<Uuid>>> {
        let optional_input = optional_input.unwrap_or_default();

        let object_id_to_idx = object_ids
            .iter()
            .enumerate()
            .map(|(idx, object_id)| (*object_id, idx))
            .collect::<HashMap<FullObjectId<'_>, usize>>();
        let mut out: Vec<Vec<Uuid>> = vec![Vec::new(); object_ids.len()];
        self.segment_id_to_liveness_and_vacuum_state
            .iter()
            .for_each(|(segment_id, info)| {
                if info.liveness.is_live == !optional_input.is_live {
                    return;
                }
                if let Some(idx) = object_id_to_idx.get(&info.liveness.object_id.as_ref()) {
                    out[*idx].push(*segment_id);
                }
            });
        Ok(out)
    }

    fn query_segment_liveness(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentLiveness>> {
        segment_ids
            .iter()
            .map(|segment_id| {
                self.segment_id_to_liveness_and_vacuum_state
                    .get(segment_id)
                    .map(|state| state.liveness.clone())
                    .ok_or_else(|| anyhow!("Segment ID does not exist: {}", segment_id))
            })
            .collect()
    }

    fn update_segment_ids(
        &mut self,
        object_id_add_remove_segment_ids: &[(FullObjectId<'_>, &[Uuid], &[Uuid])],
    ) -> Result<()> {
        // Make sure the full operation would succeed before performing it.
        for (object_id, add_segment_ids, remove_segment_ids) in object_id_add_remove_segment_ids {
            for segment_id in *remove_segment_ids {
                match self
                    .segment_id_to_liveness_and_vacuum_state
                    .get_mut(segment_id)
                {
                    Some(info) => {
                        if info.liveness.object_id.as_ref() != *object_id {
                            return Err(anyhow!(
                                "Segment ID does not belong to object: {}",
                                segment_id
                            ));
                        } else if !info.liveness.is_live {
                            return Err(anyhow!("Segment ID is already removed: {}", segment_id));
                        }
                    }
                    None => {
                        return Err(anyhow!("Segment ID does not exist: {}", segment_id));
                    }
                }
            }

            for segment_id in *add_segment_ids {
                if self
                    .segment_id_to_liveness_and_vacuum_state
                    .contains_key(segment_id)
                {
                    return Err(anyhow!("Segment ID already exists: {}", segment_id));
                }
            }
        }

        for (object_id, add_segment_ids, remove_segment_ids) in object_id_add_remove_segment_ids {
            for segment_id in *remove_segment_ids {
                self.segment_id_to_liveness_and_vacuum_state
                    .get_mut(segment_id)
                    .unwrap()
                    .liveness
                    .is_live = false;
            }

            for segment_id in *add_segment_ids {
                self.segment_id_to_liveness_and_vacuum_state.insert(
                    *segment_id,
                    SegmentLivenessAndVacuumState {
                        liveness: SegmentLiveness {
                            object_id: object_id.to_owned(),
                            is_live: true,
                        },
                        ..Default::default()
                    },
                );
            }
        }

        Ok(())
    }

    fn purge_segment_ids(&mut self, segment_ids: &[Uuid]) -> Result<()> {
        // Make sure none of the segments are live.
        for segment_id in segment_ids {
            if let Some(info) = self.segment_id_to_liveness_and_vacuum_state.get(segment_id) {
                if info.liveness.is_live {
                    return Err(anyhow!("Segment ID is still live: {}", segment_id));
                }
            }
        }

        // Now remove the segments.
        for segment_id in segment_ids {
            self.segment_id_to_liveness_and_vacuum_state
                .remove(segment_id);
        }

        Ok(())
    }

    fn query_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentMetadata>> {
        segment_ids
            .iter()
            .map(|segment_id| {
                self.segment_id_to_metadata
                    .get(segment_id)
                    .cloned()
                    .ok_or_else(|| anyhow!("Segment ID does not exist: {}", segment_id))
            })
            .collect()
    }

    fn recently_updated_objects(
        &self,
        cursor: Option<RecentObjectCursor>,
        limit: usize,
    ) -> Result<Vec<RecentObject>> {
        // Sort object_id_to_metadata descending by last_processed_xact_id filtering out any objects
        // with a last_processed_xact_id greater than max_processed_xact_id.
        let mut objects = self
            .object_id_to_metadata
            .iter()
            .filter(|(object_id, metadata)| {
                metadata.last_processed_xact_id.map_or(false, |xact_id| {
                    // Only consider objects that have been processed at all.
                    cursor.as_ref().map_or(true, |cursor| {
                        RecentObjectCursor {
                            last_processed_xact_id: xact_id,
                            object_id: object_id.as_ref(),
                        } < *cursor
                    })
                })
            })
            .map(|x| RecentObject {
                last_processed_xact_id: x.1.last_processed_xact_id.unwrap(),
                object_id: x.0.clone(),
            })
            .collect::<Vec<_>>();

        // Sort by last_processed_xact_id descending.
        objects.sort_by(|a, b| b.cmp(a));

        Ok(objects.drain(..limit.min(objects.len())).collect())
    }

    fn upsert_segment_metadatas(
        &mut self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
    ) -> Result<bool> {
        // Check CAS conditions.
        for (segment_id, update) in &updates {
            let val = self.segment_id_to_metadata.entry(*segment_id).or_default();
            if let Some((prev_index_meta, _)) = &update.last_compacted_index_meta {
                if val.last_compacted_index_meta != *prev_index_meta {
                    return Ok(false);
                }
            }
            if let Some((prev_pagination_key, _)) = update.minimum_pagination_key {
                if val.minimum_pagination_key != prev_pagination_key {
                    return Ok(false);
                }
            }
        }
        let now = Utc::now();
        // Apply updates.
        for (segment_id, update) in updates {
            let val = self.segment_id_to_metadata.get_mut(&segment_id).unwrap();
            // Apply updates.
            if let Some((_, xact_id)) = update.last_compacted_index_meta {
                val.last_compacted_index_meta = xact_id;
            }
            if let Some((_, xact_id)) = update.minimum_pagination_key {
                val.minimum_pagination_key = xact_id;
            }
            if let Some(num_rows) = update.add_num_rows {
                val.num_rows += num_rows;
            }

            if let Some(liveness_and_vacuum_state) = self
                .segment_id_to_liveness_and_vacuum_state
                .get_mut(&segment_id)
            {
                liveness_and_vacuum_state.vacuum_state.last_written_ts = now;
            }
        }
        Ok(true)
    }

    fn purge_segment_metadatas(&mut self, segment_ids: &[Uuid]) -> Result<()> {
        // Make sure none of the segments are live.
        for segment_id in segment_ids {
            if let Some(info) = self.segment_id_to_liveness_and_vacuum_state.get(segment_id) {
                if info.liveness.is_live {
                    return Err(anyhow!("Segment ID is still live: {}", segment_id));
                }
            }
        }

        // Now remove the metadatas.
        for segment_id in segment_ids {
            self.segment_id_to_metadata.remove(segment_id);
        }

        Ok(())
    }

    fn query_object_metadatas(
        &mut self,
        object_ids: &[FullObjectId<'_>],
    ) -> Result<Vec<ObjectMetadata>> {
        Ok(object_ids
            .iter()
            .map(|object_id| {
                let info = self
                    .object_id_to_metadata
                    .entry(object_id.to_owned())
                    .or_default();
                if info.wal_token.is_nil() {
                    info.wal_token = util::uuid::Uuid::new_v4();
                }
                info.clone()
            })
            .collect())
    }

    fn upsert_object_metadatas(
        &mut self,
        updates: HashMap<FullObjectId<'_>, ObjectMetadataUpdate>,
    ) -> Result<bool> {
        // Check CAS conditions.
        for (object_id, update) in &updates {
            let val = self
                .object_id_to_metadata
                .entry(object_id.to_owned())
                .or_default();
            if let Some((prev_xact_id, _)) = &update.last_processed_xact_id {
                if val.last_processed_xact_id != *prev_xact_id {
                    return Ok(false);
                }
            }
        }
        // Apply updates.
        for (object_id, update) in updates {
            let val = self
                .object_id_to_metadata
                .get_mut(&object_id.to_owned())
                .unwrap();
            if let Some((_, xact_id)) = update.last_processed_xact_id {
                val.last_processed_xact_id = xact_id;
            }
        }
        Ok(true)
    }

    fn purge_object_metadatas(&mut self, object_ids: &[FullObjectId<'_>]) -> Result<()> {
        for object_id in object_ids {
            self.object_id_to_metadata.remove(&object_id.to_owned());
        }
        Ok(())
    }

    fn query_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_ids: &[Uuid],
        ids: &[FullRowId<'_, '_>],
    ) -> Result<HashMap<FullRowIdOwned, Uuid>> {
        let membership_map = match membership_type {
            IdSegmentMembershipType::RowId => &self.row_id_to_segment_id,
            IdSegmentMembershipType::RootSpanId => &self.root_span_id_to_segment_id,
        };
        let mut out = HashMap::new();
        for id in ids {
            let id_str = id.to_string();
            if let Some(mapped_segment_ids) = membership_map.get(&id_str) {
                for segment_id in segment_ids {
                    if mapped_segment_ids.contains(segment_id) {
                        if let Some(other_segment_id) = out.insert(id.to_owned(), *segment_id) {
                            return Err(anyhow!(
                                "{} {} belongs to multiple segments: {} and {}",
                                membership_type,
                                id,
                                segment_id,
                                other_segment_id
                            ));
                        }
                    }
                }
            }
        }
        Ok(out)
    }

    fn add_id_segment_membership(
        &mut self,
        membership_type: IdSegmentMembershipType,
        segment_id_to_entries: HashMap<Uuid, Vec<FullRowId<'_, '_>>>,
    ) -> Result<()> {
        // Make sure the new ids are unique.
        let mut new_id_to_segment_ids: HashMap<String, HashSet<Uuid>> = HashMap::new();
        let membership_map = match membership_type {
            IdSegmentMembershipType::RowId => &mut self.row_id_to_segment_id,
            IdSegmentMembershipType::RootSpanId => &mut self.root_span_id_to_segment_id,
        };
        for (segment_id, ids) in segment_id_to_entries {
            for id in ids {
                let id_str = id.to_string();
                if let Some(existing_segment_ids) = membership_map.get(&id_str) {
                    if existing_segment_ids.contains(&segment_id) {
                        return Err(anyhow!(
                          "Cannot insert {} {} into segment {} because it already exists in that segment",
                          membership_type,
                          id,
                          segment_id,
                      ));
                    }
                }
                new_id_to_segment_ids
                    .entry(id_str)
                    .or_default()
                    .insert(segment_id);
            }
        }

        // If no duplicates found, proceed with the addition
        for (id, segment_ids) in new_id_to_segment_ids {
            membership_map.entry(id).or_default().extend(segment_ids);
        }
        Ok(())
    }

    fn purge_id_segment_membership(&mut self, segment_ids: &[Uuid]) -> Result<()> {
        // Remove all row IDs and root span IDs for this segment
        for (_, mapped_segment_ids) in self.row_id_to_segment_id.iter_mut() {
            for segment_id in segment_ids {
                mapped_segment_ids.remove(segment_id);
            }
        }
        self.row_id_to_segment_id
            .retain(|_, mapped_segment_ids| !mapped_segment_ids.is_empty());
        for (_, mapped_segment_ids) in self.root_span_id_to_segment_id.iter_mut() {
            for segment_id in segment_ids {
                mapped_segment_ids.remove(segment_id);
            }
        }
        self.root_span_id_to_segment_id
            .retain(|_, mapped_segment_ids| !mapped_segment_ids.is_empty());
        Ok(())
    }

    fn copy_id_segment_membership(
        &mut self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        let mut new_row_id_to_segment_ids: HashMap<String, HashSet<Uuid>> = HashMap::new();
        let mut new_root_span_id_to_segment_ids: HashMap<String, HashSet<Uuid>> = HashMap::new();
        for (id, segment_ids) in &self.row_id_to_segment_id {
            for src_segment_id in src_segment_ids {
                if segment_ids.contains(src_segment_id) {
                    new_row_id_to_segment_ids
                        .entry(id.clone())
                        .or_default()
                        .insert(dst_segment_id);
                }
            }
        }
        for (id, segment_ids) in &self.root_span_id_to_segment_id {
            for src_segment_id in src_segment_ids {
                if segment_ids.contains(src_segment_id) {
                    new_root_span_id_to_segment_ids
                        .entry(id.clone())
                        .or_default()
                        .insert(dst_segment_id);
                }
            }
        }
        for (id, new_segment_ids) in new_row_id_to_segment_ids {
            self.row_id_to_segment_id
                .entry(id)
                .or_default()
                .extend(new_segment_ids);
        }
        for (id, new_segment_ids) in new_root_span_id_to_segment_ids {
            self.root_span_id_to_segment_id
                .entry(id)
                .or_default()
                .extend(new_segment_ids);
        }
        Ok(())
    }

    fn query_segment_wal_entries_batch(
        &self,
        segment_id: Uuid,
        cursor: Option<SegmentWalEntriesCursor>,
        limit: Option<i64>,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<SegmentWalEntry>> {
        let mut out: Vec<SegmentWalEntry> = self
            .segment_id_to_wal_entries
            .get(&segment_id)
            .map(|entries| {
                entries
                    .values()
                    .flat_map(|entry| entry.values())
                    .filter(|entry| {
                        MemoryGlobalStoreData::entry_matches_filters(
                            entry,
                            &cursor,
                            &is_compacted_filter,
                        )
                    })
                    .cloned()
                    .collect()
            })
            .unwrap_or_default();
        out.sort_by_key(|entry| (entry.xact_id, entry.wal_filename));
        if let Some(limit) = limit {
            out.truncate(limit as usize);
        }
        Ok(out)
    }

    fn query_segment_wal_entries_existence(
        &self,
        segment_id_cursors: &[(Uuid, Option<SegmentWalEntriesCursor>)],
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<bool>> {
        Ok(segment_id_cursors
            .iter()
            .map(|(segment_id, cursor)| {
                self.segment_id_to_wal_entries
                    .get(segment_id)
                    .map(|entries| {
                        entries
                            .values()
                            .flat_map(|entry| entry.values())
                            .any(|entry| {
                                MemoryGlobalStoreData::entry_matches_filters(
                                    entry,
                                    cursor,
                                    &is_compacted_filter,
                                )
                            })
                    })
                    .unwrap_or(false)
            })
            .collect())
    }

    fn query_segment_wal_entries_xact_id_statistic(
        &self,
        segment_ids: &[Uuid],
        statistic: SegmentWalEntriesXactIdStatistic,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<Option<TransactionId>>> {
        Ok(segment_ids
            .iter()
            .map(|segment_id| {
                self.segment_id_to_wal_entries
                    .get(segment_id)
                    .and_then(|entries| {
                        entries
                            .values()
                            .flat_map(|entry| entry.values())
                            .filter(|entry| {
                                if entry.deleted_at.is_some() {
                                    return false;
                                }
                                if let Some(is_compacted) = &is_compacted_filter {
                                    if entry.is_compacted != *is_compacted {
                                        return false;
                                    }
                                }
                                true
                            })
                            .fold(None, |acc, entry| match statistic {
                                SegmentWalEntriesXactIdStatistic::Min => {
                                    merge_options(acc, Some(entry.xact_id), std::cmp::min)
                                }
                                SegmentWalEntriesXactIdStatistic::Max => {
                                    merge_options(acc, Some(entry.xact_id), std::cmp::max)
                                }
                            })
                    })
            })
            .collect())
    }

    fn query_purged_wal_filenames(
        &self,
        segment_id: Uuid,
        wal_filenames: &[Uuid],
    ) -> Result<Vec<Uuid>> {
        // Convert input wal_filenames to a HashSet for efficient lookup
        let input_filenames: HashSet<Uuid> = wal_filenames.iter().copied().collect();

        // Get the set of WAL filenames still in use by this segment
        let segment_wal_filenames =
            if let Some(xact_id_to_entries) = self.segment_id_to_wal_entries.get(&segment_id) {
                xact_id_to_entries
                    .values()
                    .flat_map(|entries| entries.values().map(|entry| entry.wal_filename))
                    .collect::<HashSet<Uuid>>()
            } else {
                HashSet::new()
            };

        // Find filenames that are in the input set but not in the segment's active set
        // (set difference operation)
        let purged_filenames = input_filenames
            .difference(&segment_wal_filenames)
            .copied()
            .collect();

        Ok(purged_filenames)
    }

    fn upsert_segment_wal_entries(
        &mut self,
        segment_id_to_wal_filename_to_entries: HashMap<
            Uuid,
            HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        >,
    ) -> Result<u64> {
        // Check that all xact_ids are unique in each inner map.
        for (segment_id, wal_map) in &segment_id_to_wal_filename_to_entries {
            for (wal_filename, entries) in wal_map {
                let mut xact_ids = HashSet::new();
                for entry in entries {
                    if !xact_ids.insert(entry.xact_id) {
                        return Err(anyhow!(
                            "Duplicate xact_id found in segment {} wal filename {}: {}",
                            segment_id,
                            wal_filename,
                            entry.xact_id
                        ));
                    }
                }
            }
        }

        let mut num_non_ignored_entries = 0;
        for (segment_id, wal_map) in segment_id_to_wal_filename_to_entries {
            for (wal_filename, entries) in wal_map {
                let segment_entries = self
                    .segment_id_to_wal_entries
                    .entry(segment_id)
                    .or_default();
                for entry in entries {
                    let xact_entries = segment_entries
                        .entry(entry.xact_id.to_string())
                        .or_default();
                    let wal_filename_str = wal_filename.to_string();
                    if let Some(o) = xact_entries.get_mut(&wal_filename_str) {
                        o.is_compacted = entry.is_compacted;
                        num_non_ignored_entries += 1;
                    } else {
                        // Skip the insertion if there exists another entry with the same
                        // digest on the (segment, xact_id).
                        if !xact_entries.values().any(|e| e.digest == entry.digest) {
                            xact_entries.insert(
                                wal_filename_str,
                                SegmentWalEntry {
                                    xact_id: entry.xact_id,
                                    wal_filename,
                                    byte_range_start: entry.byte_range_start,
                                    byte_range_end: entry.byte_range_end,
                                    is_compacted: entry.is_compacted,
                                    digest: entry.digest,
                                    deleted_at: None,
                                },
                            );
                            num_non_ignored_entries += 1;
                        }
                    }
                }
            }
        }
        Ok(num_non_ignored_entries)
    }

    fn update_segment_wal_entries_is_compacted_non_atomic(
        &mut self,
        segment_id_xact_id_wal_filenames: &[(Uuid, TransactionId, Uuid)],
        is_compacted: bool,
    ) -> Result<()> {
        // Since it's non-atomic, we can fail midway through if an entry doesn't
        // exist.
        for (segment_id, xact_id, wal_filename) in segment_id_xact_id_wal_filenames {
            let xact_id_str = xact_id.to_string();
            let wal_filename_str = wal_filename.to_string();
            let entry = self
                .segment_id_to_wal_entries
                .get_mut(segment_id)
                .and_then(|entries| entries.get_mut(&xact_id_str))
                .and_then(|xact_entries| xact_entries.get_mut(&wal_filename_str));
            if let Some(entry) = entry {
                entry.is_compacted = is_compacted;
            } else {
                return Err(anyhow!(
                    "Missing entry in segment_id_to_wal_entries: {:?}",
                    (segment_id, xact_id, wal_filename)
                ));
            }
        }
        Ok(())
    }

    fn update_all_segment_wal_entries_is_compacted_non_atomic(
        &mut self,
        segment_ids: &[Uuid],
        is_compacted: bool,
    ) -> Result<()> {
        let segment_ids_set = segment_ids.iter().collect::<HashSet<_>>();
        self.segment_id_to_wal_entries
            .iter_mut()
            .for_each(|(segment_id, entries)| {
                if !segment_ids_set.contains(segment_id) {
                    return;
                }
                entries.iter_mut().for_each(|(_, xact_entries)| {
                    xact_entries.values_mut().for_each(|entry| {
                        entry.is_compacted = is_compacted;
                    });
                });
            });
        Ok(())
    }

    fn purge_segment_wal_entries(&mut self, segment_ids: &[Uuid]) -> Result<()> {
        for segment_id in segment_ids {
            self.segment_id_to_wal_entries.remove(segment_id);
        }
        Ok(())
    }

    fn copy_segment_wal_entries(
        &mut self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        for src_segment_id in src_segment_ids {
            let src_entries = self
                .segment_id_to_wal_entries
                .get(src_segment_id)
                .cloned()
                .unwrap_or_default();
            let dst_entries = self
                .segment_id_to_wal_entries
                .entry(dst_segment_id)
                .or_default();
            for (xact_id_key, src_xact_entries) in src_entries {
                let dst_xact_entries = dst_entries.entry(xact_id_key).or_default();
                for (wal_filename_key, entry) in src_xact_entries {
                    dst_xact_entries
                        .entry(wal_filename_key)
                        .and_modify(|existing_entry| {
                            existing_entry.is_compacted = entry.is_compacted;
                        })
                        .or_insert(entry);
                }
            }
        }
        Ok(())
    }

    fn query_last_index_operations(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<LastIndexOperationResult>>> {
        Ok(segment_ids
            .iter()
            .map(|segment_id| {
                self.segment_id_to_last_index_operation
                    .get(segment_id)
                    .cloned()
            })
            .collect())
    }

    fn upsert_last_index_operation(
        &mut self,
        segment_id: Uuid,
        operation: LastIndexOperation,
        op_token: Uuid,
        op_token_opts: LastIndexOpTokenOpts,
    ) -> Result<()> {
        let entry = self.segment_id_to_last_index_operation.entry(segment_id);

        let now = Utc::now();
        match entry {
            std::collections::hash_map::Entry::Occupied(mut o) => {
                let result = o.get_mut();
                if !op_token_opts.always_update {
                    if let Some(current_op_token) = &result.current_op_token {
                        if *current_op_token != op_token {
                            return Ok(());
                        }
                    }
                }
                if result.current_op_token != Some(op_token) {
                    result.start = now;
                }
                result.last_updated = now;
                if op_token_opts.clear_value {
                    result.current_op_token = None;
                } else {
                    result.current_op_token = Some(op_token);
                }
                result.operation = operation;
            }
            std::collections::hash_map::Entry::Vacant(v) => {
                v.insert(LastIndexOperationResult {
                    start: now,
                    last_updated: now,
                    operation,
                    current_op_token: Some(op_token),
                });
            }
        }
        Ok(())
    }

    fn bump_last_index_operation_updated_ts(
        &mut self,
        segment_id: Uuid,
        op_token: Uuid,
    ) -> Result<()> {
        if let Some(result) = self.segment_id_to_last_index_operation.get_mut(&segment_id) {
            if result.current_op_token == Some(op_token) {
                result.last_updated = Utc::now();
            }
        }
        Ok(())
    }

    fn query_field_statistics(
        &self,
        segment_ids: &[Uuid],
        field_names: &[&str],
    ) -> Result<HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>> {
        let mut result = HashMap::new();
        for segment_id in segment_ids {
            if let Some(field_stats) = self.segment_id_to_field_name_to_statistics.get(segment_id) {
                let mut matching_stats = HashMap::new();
                for field_name in field_names {
                    if let Some(stats) = field_stats.get(*field_name) {
                        matching_stats.insert(field_name.to_string(), stats.clone());
                    }
                }
                if !matching_stats.is_empty() {
                    result.insert(*segment_id, matching_stats);
                }
            }
        }
        Ok(result)
    }

    fn upsert_field_statistics(
        &mut self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
    ) {
        for (segment_id, field_name, statistics) in segment_id_field_name_statistics {
            self.segment_id_to_field_name_to_statistics
                .entry(segment_id)
                .or_default()
                .insert(field_name.to_string(), statistics);
        }
    }

    fn upsert_segment_metadatas_and_field_statistics(
        &mut self,
        updates: HashMap<Uuid, (SegmentMetadataUpdate, Vec<(&str, SegmentFieldStatistics)>)>,
    ) -> Result<bool> {
        let mut metadata_updates = HashMap::new();
        let mut field_statistics_updates = Vec::new();
        for (segment_id, (metadata_update, field_statistics)) in updates {
            metadata_updates.insert(segment_id, metadata_update);
            field_statistics_updates.extend(
                field_statistics
                    .iter()
                    .map(|(field_name, statistics)| (segment_id, *field_name, statistics.clone())),
            );
        }

        let applied = self.upsert_segment_metadatas(metadata_updates)?;
        if applied {
            self.upsert_field_statistics(field_statistics_updates);
        }
        Ok(applied)
    }

    fn entry_matches_filters(
        entry: &SegmentWalEntry,
        cursor: &Option<SegmentWalEntriesCursor>,
        is_compacted_filter: &Option<bool>,
    ) -> bool {
        if entry.deleted_at.is_some() {
            return false;
        }
        if let Some(cursor) = cursor {
            match cursor {
                SegmentWalEntriesCursor::XactIdGe(xact_id) => {
                    if entry.xact_id < *xact_id {
                        return false;
                    }
                }
                SegmentWalEntriesCursor::XactIdWalFilenameGt {
                    xact_id,
                    wal_filename,
                } => {
                    if (entry.xact_id, entry.wal_filename) <= (*xact_id, *wal_filename) {
                        return false;
                    }
                }
            }
        }
        if let Some(is_compacted) = is_compacted_filter {
            if entry.is_compacted != *is_compacted {
                return false;
            }
        }
        true
    }

    fn count_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        let mut count = 0;
        for segment_id in segment_ids {
            if let Some(wal_entries) = self.segment_id_to_wal_entries.get(segment_id) {
                for (xact_id_str, wal_filename_entries) in wal_entries.iter() {
                    let entry_xact_id = xact_id_str
                        .parse::<TransactionId>()
                        .expect("Failed to parse transaction ID");
                    if entry_xact_id < xact_id {
                        count += wal_filename_entries
                            .values()
                            .filter(|entry| entry.deleted_at.is_none())
                            .count();
                    }
                }
            }
        }
        Ok(count as u64)
    }

    fn delete_segment_wal_entries_up_to_xact_id(
        &mut self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        if segment_ids.is_empty() {
            return Ok(0);
        }
        let mut num_deleted = 0;
        let now = Utc::now();
        for segment_id in segment_ids {
            if let Some(wal_entries) = self.segment_id_to_wal_entries.get_mut(segment_id) {
                for (xact_id_str, wal_filename_entries) in wal_entries.iter_mut() {
                    let entry_xact_id = xact_id_str
                        .parse::<TransactionId>()
                        .expect("Failed to parse transaction ID");
                    if entry_xact_id < xact_id {
                        for entry in wal_filename_entries.values_mut() {
                            if entry.deleted_at.is_none() {
                                entry.deleted_at = Some(now);
                                num_deleted += 1;
                            }
                        }
                    }
                }
            } else {
                log::warn!(
                    "Skipping purge for segment {} because no WAL entries were found in metadata",
                    segment_id
                );
            }
        }
        Ok(num_deleted as u64)
    }

    fn purge_deleted_segment_wal_entries(
        &mut self,
        segment_ids: &[Uuid],
        expiration_seconds: u64,
    ) -> Result<u64> {
        let expiration_seconds_i64 = i64::try_from(expiration_seconds)?;
        let cutoff_time = Utc::now() - Duration::seconds(expiration_seconds_i64);
        let mut total_purged = 0;

        for segment_id in segment_ids {
            if let Some(xact_map) = self.segment_id_to_wal_entries.get_mut(segment_id) {
                for wal_file_map in xact_map.values_mut() {
                    let before_len = wal_file_map.len();
                    wal_file_map.retain(|_, entry| {
                        entry
                            .deleted_at
                            .map(|deleted_at| deleted_at > cutoff_time)
                            .unwrap_or(true)
                    });
                    total_purged += before_len - wal_file_map.len();
                }
            }
        }

        Ok(total_purged as u64)
    }

    fn restore_segment_wal_entries_up_to_xact_id(
        &mut self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        if segment_ids.is_empty() {
            return Ok(0);
        }

        let mut num_restored = 0;
        for segment_id in segment_ids {
            if let Some(wal_entries) = self.segment_id_to_wal_entries.get_mut(segment_id) {
                for (xact_id_str, wal_filename_entries) in wal_entries.iter_mut() {
                    let entry_xact_id = xact_id_str
                        .parse::<TransactionId>()
                        .expect("Failed to parse transaction ID");

                    // Check if xact_id < threshold (exclusive upper bound)
                    if entry_xact_id >= xact_id {
                        continue;
                    }

                    for entry in wal_filename_entries.values_mut() {
                        if entry.deleted_at.is_some() {
                            entry.deleted_at = None;
                            num_restored += 1;
                        }
                    }
                }
            }
        }
        Ok(num_restored)
    }

    fn query_vacuum_index_segment_ids(
        &self,
        limit: usize,
        max_last_successful_start_minus_last_written_seconds: i64,
        vacuum_period_seconds: i64,
        start_ts: DateTime<Utc>,
        object_ids: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<Uuid>> {
        let mut eligible_segments: Vec<(Uuid, Duration)> = Vec::new();

        let object_id_set: Option<HashSet<String>> =
            object_ids.map(|ids| ids.iter().map(|id| id.to_string()).collect());

        for (segment_id, liveness_and_vacuum_state) in
            self.segment_id_to_liveness_and_vacuum_state.iter()
        {
            let liveness = &liveness_and_vacuum_state.liveness;
            let vacuum_state = &liveness_and_vacuum_state.vacuum_state;

            if !liveness.is_live {
                continue;
            }

            // If object_ids is provided, skip segments that don't belong to any of those object_ids.
            if let Some(ref object_id_set) = object_id_set {
                if !object_id_set.contains(&liveness.object_id.to_string()) {
                    continue;
                }
            }

            let last_successful_start_ts = vacuum_state.vacuum_index_last_successful_start_ts;
            let last_successful_start_minus_last_written =
                last_successful_start_ts - vacuum_state.last_written_ts;

            if last_successful_start_minus_last_written
                < Duration::seconds(max_last_successful_start_minus_last_written_seconds)
                && last_successful_start_ts < start_ts - Duration::seconds(vacuum_period_seconds)
            {
                eligible_segments.push((*segment_id, last_successful_start_minus_last_written));
            }
        }

        // Sort by `last_successful_start_minus_last_written` ascending.
        eligible_segments.sort_by(|a, b| a.1.cmp(&b.1));

        let result = eligible_segments
            .into_iter()
            .take(limit)
            .map(|(id, _)| id)
            .collect();

        Ok(result)
    }

    fn query_segment_vacuum_state(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, SegmentVacuumState>> {
        let mut result = HashMap::new();
        for &segment_id in segment_ids {
            if let Some(liveness_and_vacuum_state) = self
                .segment_id_to_liveness_and_vacuum_state
                .get(&segment_id)
            {
                result.insert(segment_id, liveness_and_vacuum_state.vacuum_state.clone());
            }
        }
        Ok(result)
    }

    fn upsert_segment_vacuum_last_successful_start_ts(
        &mut self,
        segment_ids: &[Uuid],
        vacuum_type: VacuumType,
        last_successful_start_ts: DateTime<Utc>,
    ) -> Result<()> {
        if segment_ids.is_empty() {
            return Ok(());
        }

        for &segment_id in segment_ids {
            let existing_liveness_and_vacuum_state = self
                .segment_id_to_liveness_and_vacuum_state
                .entry(segment_id)
                .or_default();
            let existing_vacuum_state = &mut existing_liveness_and_vacuum_state.vacuum_state;
            match vacuum_type {
                VacuumType::VacuumIndex => {
                    existing_vacuum_state.vacuum_index_last_successful_start_ts =
                        last_successful_start_ts;
                }
            }
        }

        Ok(())
    }

    fn upsert_segment_last_written_ts(
        &mut self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
    ) -> Result<()> {
        let last_written_ts = last_written_ts.unwrap_or_else(|| Utc::now());
        for &segment_id in segment_ids {
            let liveness_and_vacuum_state = self
                .segment_id_to_liveness_and_vacuum_state
                .entry(segment_id)
                .or_default();
            liveness_and_vacuum_state.vacuum_state.last_written_ts = last_written_ts;
        }
        Ok(())
    }

    fn list_object_ids(
        &self,
        object_id_cursor: Option<FullObjectId<'_>>,
        limit: usize,
        object_ids_filter: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<FullObjectIdOwned>> {
        let mut object_ids: Vec<FullObjectIdOwned> = self
            .object_id_to_metadata
            .keys()
            .filter(|id| object_ids_filter.map_or(true, |filter| filter.contains(&id.as_ref())))
            .cloned()
            .collect();

        object_ids.sort();

        let start_idx = object_id_cursor.map_or(0, |cursor| {
            object_ids
                .binary_search_by(|id| id.as_ref().cmp(&cursor))
                .map_or_else(|insert_pos| insert_pos, |exact_match| exact_match + 1)
        });

        if start_idx >= object_ids.len() {
            Ok(vec![])
        } else {
            let end_idx = std::cmp::min(start_idx + limit, object_ids.len());
            Ok(object_ids[start_idx..end_idx].to_vec())
        }
    }

    fn list_object_segment_ids(
        &self,
        object_id: FullObjectId<'_>,
        segment_id_cursor: Uuid,
        limit: usize,
    ) -> Result<Vec<Uuid>> {
        let mut candidates: Vec<Uuid> = self
            .segment_id_to_liveness_and_vacuum_state
            .iter()
            .filter_map(|(segment_id, liveness_and_vacuum_state)| {
                let liveness = &liveness_and_vacuum_state.liveness;
                if !liveness.is_live || liveness.object_id.as_ref() != object_id {
                    return None;
                }

                if segment_id > &segment_id_cursor {
                    Some(*segment_id)
                } else {
                    None
                }
            })
            .collect();

        candidates.sort_unstable();
        candidates.truncate(limit);

        Ok(candidates)
    }

    fn list_object_ids_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        limit: usize,
    ) -> Result<Vec<(FullObjectIdOwned, Uuid)>> {
        let object_ids_set: HashSet<FullObjectId<'_>> = object_ids.iter().copied().collect();

        let mut candidates: Vec<(FullObjectIdOwned, Uuid)> = self
            .segment_id_to_liveness_and_vacuum_state
            .iter()
            .filter_map(|(segment_id, liveness_and_vacuum_state)| {
                let liveness = &liveness_and_vacuum_state.liveness;
                if liveness.is_live && object_ids_set.contains(&liveness.object_id.as_ref()) {
                    Some((liveness.object_id.clone(), *segment_id))
                } else {
                    None
                }
            })
            .collect();

        candidates.sort_by(|a, b| a.0.cmp(&b.0).then(a.1.cmp(&b.1)));
        candidates.truncate(limit);

        Ok(candidates)
    }

    fn query_time_based_retention_state(&self) -> Result<TimeBasedRetentionState> {
        Ok(self.time_based_retention_state.clone())
    }

    fn upsert_time_based_retention_state(&mut self, state: &TimeBasedRetentionState) -> Result<()> {
        self.time_based_retention_state = state.clone();
        Ok(())
    }

    fn query_segment_task_infos(&self, segment_ids: &[Uuid]) -> Result<Vec<TaskInfos>> {
        Ok(segment_ids
            .iter()
            .map(|segment_id| {
                self.segment_id_to_task_infos
                    .get(segment_id)
                    .cloned()
                    .unwrap_or_default()
            })
            .collect())
    }

    fn upsert_segment_task_info(&mut self, segment_ids: &[Uuid], info: &TaskInfo) -> Result<()> {
        for &segment_id in segment_ids {
            let task_infos = self.segment_id_to_task_infos.entry(segment_id).or_default();

            match info {
                TaskInfo::TimeBasedRetention(retention_info) => {
                    task_infos.time_based_retention = Some(retention_info.clone());
                }
                TaskInfo::VacuumIndex(vacuum_index_info) => {
                    task_infos.vacuum_index = Some(vacuum_index_info.clone());
                }
            }
        }
        Ok(())
    }

    fn query_unbackfilled_tracking_entries_ordered(
        &self,
        has_completed_initial_backfill: bool,
        cursor: Option<BackfillTrackingEntryId<'_>>,
        limit: usize,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        // Collect all tracking entries beyond the cursor. Sort them, and then
        // grab the first 'limit' items.
        let mut filtered_entries: Vec<&BackfillTrackingEntry> = self
            .backfill_tracking_entries
            .values()
            .flat_map(|inner| {
                inner.values().filter(|entry| {
                    if entry.completed_initial_backfill_ts.is_some()
                        != has_completed_initial_backfill
                        || (entry.last_processed_sequence_id >= entry.last_encountered_sequence_id
                            && entry.last_processed_sequence_id_2
                                >= entry.last_encountered_sequence_id_2)
                    {
                        return false;
                    }
                    if let Some(c) = &cursor {
                        if entry.id() <= *c {
                            return false;
                        }
                    }
                    true
                })
            })
            .collect();
        filtered_entries.sort_unstable_by(|lhs, rhs| lhs.id().cmp(&rhs.id()));
        filtered_entries.truncate(limit);
        Ok(filtered_entries.into_iter().cloned().collect())
    }

    fn update_backfill_tracking_entries(
        &mut self,
        updates: Vec<(BackfillTrackingEntryId<'_>, BackfillTrackingEntryUpdate)>,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        // Make sure all the named entries exist before doing any updates.
        for (entry_id, _) in &updates {
            if let Some(inner) = self.backfill_tracking_entries.get(entry_id.project_id) {
                if inner.contains_key(&entry_id.object_type) {
                    continue;
                }
            }
            return Err(anyhow!(
                "No existing tracking entry found for {:?}",
                entry_id
            ));
        }

        // Now do the updates.
        let mut updated_entries = Vec::with_capacity(updates.len());
        for (entry_id, update) in updates {
            let inner = self
                .backfill_tracking_entries
                .get_mut(entry_id.project_id)
                .unwrap();
            let entry = inner.get_mut(&entry_id.object_type).unwrap();
            if let Some(value) = update.last_processed_sequence_id {
                entry.last_processed_sequence_id = value;
            }
            if let Some(value) = update.last_processed_sequence_id_2 {
                entry.last_processed_sequence_id_2 = value;
            }
            updated_entries.push(entry.clone());
        }

        Ok(updated_entries)
    }

    fn query_backfill_tracking_entries_by_ids(
        &self,
        entries: &[BackfillTrackingEntryId<'_>],
    ) -> Result<Vec<Option<BackfillTrackingEntry>>> {
        let mut result = Vec::with_capacity(entries.len());
        for entry_id in entries {
            let tracking_entry = self
                .backfill_tracking_entries
                .get(entry_id.project_id)
                .and_then(|inner| inner.get(&entry_id.object_type))
                .cloned();
            result.push(tracking_entry);
        }
        Ok(result)
    }

    fn query_backfill_brainstore_objects(
        &self,
        tracking_entries: &[BackfillTrackingEntryId<'_>],
        is_logs2: bool,
        min_sequence_id: i64,
        max_sequence_id: i64,
    ) -> Result<Vec<Vec<BackfillBrainstoreObject>>> {
        let mut tracking_entry_to_objects: HashMap<
            BackfillTrackingEntryId,
            HashMap<FullObjectId, BackfillBrainstoreObject>,
        > = HashMap::new();
        for tracking_entry in tracking_entries {
            tracking_entry_to_objects.insert(tracking_entry.clone(), HashMap::new());
        }
        for backfill_object in self.backfill_brainstore_objects.iter() {
            if !(backfill_object.is_logs2 == is_logs2
                && backfill_object.sequence_id <= max_sequence_id
                && backfill_object.sequence_id >= min_sequence_id)
            {
                continue;
            }
            let tracking_entry_id = BackfillTrackingEntryId {
                project_id: &backfill_object.project_id,
                object_type: backfill_object.object_id.object_type,
            };
            if let Some(backfill_objects) = tracking_entry_to_objects.get_mut(&tracking_entry_id) {
                if let Some(existing_entry) =
                    backfill_objects.get_mut(&backfill_object.object_id.as_ref())
                {
                    existing_entry.min_sequence_id =
                        std::cmp::min(existing_entry.min_sequence_id, backfill_object.sequence_id);
                    existing_entry.max_sequence_id =
                        std::cmp::max(existing_entry.max_sequence_id, backfill_object.sequence_id);
                    existing_entry.min_xact_id =
                        std::cmp::min(existing_entry.min_xact_id, backfill_object.xact_id);
                    existing_entry.max_xact_id =
                        std::cmp::max(existing_entry.max_xact_id, backfill_object.xact_id);
                } else {
                    backfill_objects.insert(
                        backfill_object.object_id.as_ref(),
                        BackfillBrainstoreObject {
                            project_id: backfill_object.project_id.clone(),
                            object_id: backfill_object.object_id.clone(),
                            is_logs2: backfill_object.is_logs2,
                            min_sequence_id: backfill_object.sequence_id,
                            max_sequence_id: backfill_object.sequence_id,
                            min_xact_id: backfill_object.xact_id,
                            max_xact_id: backfill_object.xact_id,
                        },
                    );
                }
            }
        }
        let mut result = Vec::new();
        for tracking_entry in tracking_entries {
            let objects = tracking_entry_to_objects.remove(tracking_entry).unwrap();
            result.push(objects.into_iter().map(|(_, object)| object).collect());
        }
        Ok(result)
    }

    #[cfg(test)]
    fn testing_only_insert_backfill_data(
        &mut self,
        tracking_entries: Vec<BackfillTrackingEntry>,
        brainstore_objects: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
    ) -> Result<()> {
        for tracking_entry in tracking_entries {
            self.backfill_tracking_entries
                .entry(tracking_entry.project_id.clone())
                .or_default()
                .insert(tracking_entry.object_type, tracking_entry);
        }
        self.backfill_brainstore_objects.extend(brainstore_objects);
        Ok(())
    }
}

#[async_trait]
impl GlobalStore for MemoryGlobalStore {
    async fn list_segment_ids_global(
        &self,
        optional_input: Option<ListSegmentIdsGlobalOptionalInput>,
    ) -> Result<Vec<Uuid>> {
        let data = self.data.read().await;
        data.list_segment_ids_global(optional_input)
    }

    async fn list_segment_ids(
        &self,
        object_ids: &[FullObjectId],
        optional_input: Option<ListSegmentIdsOptionalInput>,
    ) -> Result<Vec<Vec<Uuid>>> {
        let data = self.data.read().await;
        data.list_segment_ids(object_ids, optional_input)
    }

    async fn query_segment_liveness(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentLiveness>> {
        let data = self.data.read().await;
        data.query_segment_liveness(segment_ids)
    }

    async fn update_segment_ids(
        &self,
        object_id_add_remove_segment_ids: &[(FullObjectId, &[Uuid], &[Uuid])],
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.update_segment_ids(object_id_add_remove_segment_ids)
    }

    async fn purge_segment_ids(&self, segment_ids: &[Uuid]) -> Result<()> {
        let mut data = self.data.write().await;
        data.purge_segment_ids(segment_ids)
    }

    async fn query_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentMetadata>> {
        let data = self.data.read().await;
        data.query_segment_metadatas(segment_ids)
    }

    async fn query_recently_updated_objects<'a>(
        &self,
        max_compacted_xact_id: Option<RecentObjectCursor<'a>>,
        limit: usize,
    ) -> Result<Vec<RecentObject>> {
        let data = self.data.read().await;
        data.recently_updated_objects(max_compacted_xact_id, limit)
    }

    async fn upsert_segment_metadatas(
        &self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
    ) -> Result<bool> {
        let mut data = self.data.write().await;
        data.upsert_segment_metadatas(updates)
    }

    async fn purge_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<()> {
        let mut data = self.data.write().await;
        data.purge_segment_metadatas(segment_ids)
    }

    async fn query_object_metadatas(
        &self,
        object_ids: &[FullObjectId],
    ) -> Result<Vec<ObjectMetadata>> {
        let mut data = self.data.write().await;
        data.query_object_metadatas(object_ids)
    }

    async fn upsert_object_metadatas(
        &self,
        updates: HashMap<FullObjectId<'_>, ObjectMetadataUpdate>,
    ) -> Result<bool> {
        let mut data = self.data.write().await;
        data.upsert_object_metadatas(updates)
    }

    async fn purge_object_metadatas(&self, object_ids: &[FullObjectId]) -> Result<()> {
        let mut data = self.data.write().await;
        data.purge_object_metadatas(object_ids)
    }

    async fn query_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_ids: &[Uuid],
        ids: &[FullRowId<'_, '_>],
    ) -> Result<HashMap<FullRowIdOwned, Uuid>> {
        let data = self.data.read().await;
        data.query_id_segment_membership(membership_type, segment_ids, ids)
    }

    async fn add_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_id_to_entries: HashMap<Uuid, Vec<FullRowId<'_, '_>>>,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.add_id_segment_membership(membership_type, segment_id_to_entries)
    }

    async fn purge_id_segment_membership(&self, segment_ids: &[Uuid]) -> Result<()> {
        let mut data = self.data.write().await;
        data.purge_id_segment_membership(segment_ids)
    }

    async fn copy_id_segment_membership(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.copy_id_segment_membership(src_segment_ids, dst_segment_id)
    }

    async fn query_segment_wal_entries_batch(
        &self,
        segment_id: Uuid,
        cursor: Option<SegmentWalEntriesCursor>,
        limit: Option<i64>,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<SegmentWalEntry>> {
        let data = self.data.read().await;
        data.query_segment_wal_entries_batch(segment_id, cursor, limit, is_compacted_filter)
    }

    async fn query_segment_wal_entries_existence(
        &self,
        segment_id_cursors: &[(Uuid, Option<SegmentWalEntriesCursor>)],
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<bool>> {
        let data = self.data.read().await;
        data.query_segment_wal_entries_existence(segment_id_cursors, is_compacted_filter)
    }

    async fn query_segment_wal_entries_xact_id_statistic(
        &self,
        segment_ids: &[Uuid],
        statistic: SegmentWalEntriesXactIdStatistic,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<Option<TransactionId>>> {
        let data = self.data.read().await;
        data.query_segment_wal_entries_xact_id_statistic(
            segment_ids,
            statistic,
            is_compacted_filter,
        )
    }

    async fn query_purged_wal_filenames(
        &self,
        segment_id: Uuid,
        wal_filenames: &[Uuid],
    ) -> Result<Vec<Uuid>> {
        let data = self.data.read().await;
        data.query_purged_wal_filenames(segment_id, wal_filenames)
    }

    async fn upsert_segment_wal_entries(
        &self,
        segment_id_to_wal_filename_to_entries: HashMap<
            Uuid,
            HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        >,
    ) -> Result<u64> {
        let mut data = self.data.write().await;
        data.upsert_segment_wal_entries(segment_id_to_wal_filename_to_entries)
    }

    async fn update_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_id_xact_id_wal_filenames: &[(Uuid, TransactionId, Uuid)],
        is_compacted: bool,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.update_segment_wal_entries_is_compacted_non_atomic(
            segment_id_xact_id_wal_filenames,
            is_compacted,
        )
    }

    async fn update_all_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_ids: &[Uuid],
        is_compacted: bool,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.update_all_segment_wal_entries_is_compacted_non_atomic(segment_ids, is_compacted)
    }

    async fn purge_segment_wal_entries(&self, segment_ids: &[Uuid]) -> Result<()> {
        let mut data = self.data.write().await;
        data.purge_segment_wal_entries(segment_ids)
    }

    async fn copy_segment_wal_entries(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.copy_segment_wal_entries(src_segment_ids, dst_segment_id)
    }

    async fn query_last_index_operations(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<LastIndexOperationResult>>> {
        let data = self.data.read().await;
        data.query_last_index_operations(segment_ids)
    }

    async fn upsert_last_index_operation(
        &self,
        segment_id: Uuid,
        operation: LastIndexOperation,
        op_token: Uuid,
        op_token_opts: LastIndexOpTokenOpts,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_last_index_operation(segment_id, operation, op_token, op_token_opts)
    }

    async fn bump_last_index_operation_updated_ts(
        &self,
        segment_id: Uuid,
        op_token: Uuid,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.bump_last_index_operation_updated_ts(segment_id, op_token)
    }

    async fn query_field_statistics(
        &self,
        segment_ids: &[Uuid],
        field_names: &[&str],
    ) -> Result<HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>> {
        let data = self.data.read().await;
        data.query_field_statistics(segment_ids, field_names)
    }

    async fn upsert_field_statistics(
        &self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_field_statistics(segment_id_field_name_statistics);
        Ok(())
    }

    async fn upsert_segment_metadatas_and_field_statistics(
        &self,
        updates: HashMap<Uuid, (SegmentMetadataUpdate, Vec<(&str, SegmentFieldStatistics)>)>,
    ) -> Result<bool> {
        let mut data = self.data.write().await;
        data.upsert_segment_metadatas_and_field_statistics(updates)
    }

    async fn count_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        let data = self.data.read().await;
        data.count_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)
    }

    async fn delete_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        let mut data = self.data.write().await;
        data.delete_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)
    }

    async fn purge_deleted_segment_wal_entries(
        &self,
        segment_ids: &[Uuid],
        expiration_seconds: u64,
    ) -> Result<u64> {
        let mut data = self.data.write().await;
        data.purge_deleted_segment_wal_entries(segment_ids, expiration_seconds)
    }

    async fn restore_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        let mut data = self.data.write().await;
        data.restore_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)
    }

    async fn query_vacuum_index_segment_ids(
        &self,
        limit: usize,
        max_last_successful_start_minus_last_written_seconds: i64,
        vacuum_period_seconds: i64,
        start_ts: DateTime<Utc>,
        object_ids: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<Uuid>> {
        let data = self.data.read().await;
        data.query_vacuum_index_segment_ids(
            limit,
            max_last_successful_start_minus_last_written_seconds,
            vacuum_period_seconds,
            start_ts,
            object_ids,
        )
    }

    async fn query_segment_vacuum_state(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, SegmentVacuumState>> {
        let data = self.data.read().await;
        data.query_segment_vacuum_state(segment_ids)
    }

    async fn upsert_segment_vacuum_last_successful_start_ts(
        &self,
        segment_ids: &[Uuid],
        vacuum_type: VacuumType,
        last_successful_start_ts: DateTime<Utc>,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_segment_vacuum_last_successful_start_ts(
            segment_ids,
            vacuum_type,
            last_successful_start_ts,
        )
    }

    async fn upsert_segment_last_written_ts(
        &self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_segment_last_written_ts(segment_ids, last_written_ts)
    }

    async fn list_object_ids(
        &self,
        object_id_cursor: Option<FullObjectId<'_>>,
        limit: usize,
        object_ids_filter: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<FullObjectIdOwned>> {
        let data = self.data.read().await;
        data.list_object_ids(object_id_cursor, limit, object_ids_filter)
    }

    async fn list_object_segment_ids(
        &self,
        object_id: FullObjectId<'_>,
        segment_id_cursor: Uuid,
        limit: usize,
    ) -> Result<Vec<Uuid>> {
        let data = self.data.read().await;
        data.list_object_segment_ids(object_id, segment_id_cursor, limit)
    }

    async fn list_object_ids_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        limit: usize,
    ) -> Result<Vec<(FullObjectIdOwned, Uuid)>> {
        let data = self.data.read().await;
        data.list_object_ids_segment_ids(object_ids, limit)
    }

    async fn query_time_based_retention_state(&self) -> Result<TimeBasedRetentionState> {
        let data = self.data.read().await;
        data.query_time_based_retention_state()
    }

    async fn upsert_time_based_retention_state(
        &self,
        state: &TimeBasedRetentionState,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_time_based_retention_state(state)
    }

    async fn query_segment_task_infos(&self, segment_ids: &[Uuid]) -> Result<Vec<TaskInfos>> {
        let data = self.data.read().await;
        data.query_segment_task_infos(segment_ids)
    }

    async fn upsert_segment_task_info(&self, segment_ids: &[Uuid], info: &TaskInfo) -> Result<()> {
        let mut data = self.data.write().await;
        data.upsert_segment_task_info(segment_ids, info)
    }

    async fn query_unbackfilled_tracking_entries_ordered(
        &self,
        has_completed_initial_backfill: bool,
        cursor: Option<BackfillTrackingEntryId<'_>>,
        limit: usize,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        let data = self.data.read().await;
        data.query_unbackfilled_tracking_entries_ordered(
            has_completed_initial_backfill,
            cursor,
            limit,
        )
    }

    async fn update_backfill_tracking_entries(
        &self,
        updates: Vec<(BackfillTrackingEntryId<'_>, BackfillTrackingEntryUpdate)>,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        let mut data = self.data.write().await;
        data.update_backfill_tracking_entries(updates)
    }

    async fn query_backfill_tracking_entries_by_ids(
        &self,
        entries: &[BackfillTrackingEntryId<'_>],
    ) -> Result<Vec<Option<BackfillTrackingEntry>>> {
        let data = self.data.read().await;
        data.query_backfill_tracking_entries_by_ids(entries)
    }

    async fn query_backfill_brainstore_objects(
        &self,
        tracking_entries: &[BackfillTrackingEntryId<'_>],
        is_logs2: bool,
        min_sequence_id: i64,
        max_sequence_id: i64,
    ) -> Result<Vec<Vec<BackfillBrainstoreObject>>> {
        let data = self.data.read().await;
        data.query_backfill_brainstore_objects(
            tracking_entries,
            is_logs2,
            min_sequence_id,
            max_sequence_id,
        )
    }

    #[cfg(test)]
    async fn testing_only_insert_backfill_data(
        &self,
        tracking_entries: Vec<BackfillTrackingEntry>,
        brainstore_objects: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
    ) -> Result<()> {
        let mut data = self.data.write().await;
        data.testing_only_insert_backfill_data(tracking_entries, brainstore_objects)
    }

    async fn status(&self) -> Result<String> {
        Ok("MemoryGlobalStore is ok".to_string())
    }
}

impl Instrumented for MemoryGlobalStore {
    fn enable_timing(&self) {}

    fn reset_timing(&self) {}

    fn timers(&self) -> Vec<Arc<crate::timer::TimerManager>> {
        vec![]
    }
}

#[derive(Debug, Clone)]
pub struct DirectoryGlobalStore {
    pub directory: AsyncDirectoryArc,
    pub locks_manager: Arc<dyn GlobalLocksManager>,
    pub metadata_prefix: PathBuf,
}

impl DirectoryGlobalStore {
    const GLOBAL_STORE_DATA_FILE: &str = "global_store_data";

    pub fn new(
        directory: AsyncDirectoryArc,
        locks_manager: Arc<dyn GlobalLocksManager>,
        metadata_prefix: PathBuf,
    ) -> Self {
        DirectoryGlobalStore {
            directory,
            locks_manager,
            metadata_prefix,
        }
    }

    async fn read_metadata_file<T: serde::de::DeserializeOwned>(
        &self,
        suffix: &str,
    ) -> Result<Option<T>> {
        let path = self.metadata_prefix.join(suffix);
        let value = read_json_value(self.directory.as_ref(), &path).await?;
        Ok(value.map(|v| serde_json::from_value(v)).transpose()?)
    }

    async fn write_metadata_file<T: serde::Serialize>(&self, suffix: &str, data: T) -> Result<()> {
        let path = self.metadata_prefix.join(suffix);
        write_json_value(
            self.directory.as_ref(),
            &path,
            &serde_json::to_value(&data)?,
        )
        .await
    }

    async fn load_global_store_data(&self) -> Result<MemoryGlobalStoreData> {
        Ok(self
            .read_metadata_file::<MemoryGlobalStoreData>(Self::GLOBAL_STORE_DATA_FILE)
            .await?
            .unwrap_or_default())
    }

    async fn write_global_store_data(&self, data: MemoryGlobalStoreData) -> Result<()> {
        self.write_metadata_file::<MemoryGlobalStoreData>(Self::GLOBAL_STORE_DATA_FILE, data)
            .await
    }

    // Note: we don't need to take read locks because reading the individual file is atomic. We
    // only need to prevent write operations from interleaving with each other.
    async fn write_lock_global_store_data(&self) -> Result<Box<dyn GlobalLockWriteGuard>> {
        self.locks_manager.write(Self::GLOBAL_STORE_DATA_FILE).await
    }
}

#[async_trait]
impl GlobalStore for DirectoryGlobalStore {
    async fn list_segment_ids_global(
        &self,
        optional_input: Option<ListSegmentIdsGlobalOptionalInput>,
    ) -> Result<Vec<Uuid>> {
        let data = self.load_global_store_data().await?;
        data.list_segment_ids_global(optional_input)
    }

    async fn list_segment_ids(
        &self,
        object_ids: &[FullObjectId],
        optional_input: Option<ListSegmentIdsOptionalInput>,
    ) -> Result<Vec<Vec<Uuid>>> {
        let data = self.load_global_store_data().await?;
        data.list_segment_ids(object_ids, optional_input)
    }

    async fn query_segment_liveness(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentLiveness>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_liveness(segment_ids)
    }

    async fn update_segment_ids(
        &self,
        object_id_add_remove_segment_ids: &[(FullObjectId, &[Uuid], &[Uuid])],
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.update_segment_ids(object_id_add_remove_segment_ids)?;
        self.write_global_store_data(data).await
    }

    async fn purge_segment_ids(&self, segment_ids: &[Uuid]) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.purge_segment_ids(segment_ids)?;
        self.write_global_store_data(data).await
    }

    async fn query_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<Vec<SegmentMetadata>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_metadatas(segment_ids)
    }

    async fn upsert_segment_metadatas(
        &self,
        updates: HashMap<Uuid, SegmentMetadataUpdate>,
    ) -> Result<bool> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.upsert_segment_metadatas(updates)?;
        self.write_global_store_data(data).await?;
        Ok(res)
    }

    async fn purge_segment_metadatas(&self, segment_ids: &[Uuid]) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.purge_segment_metadatas(segment_ids)?;
        self.write_global_store_data(data).await
    }

    async fn query_object_metadatas(
        &self,
        object_ids: &[FullObjectId],
    ) -> Result<Vec<ObjectMetadata>> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.query_object_metadatas(object_ids);
        self.write_global_store_data(data).await?;
        res
    }

    async fn query_recently_updated_objects<'a>(
        &self,
        cursor: Option<RecentObjectCursor<'a>>,
        limit: usize,
    ) -> Result<Vec<RecentObject>> {
        let _guard = self.write_lock_global_store_data().await?;
        let data = self.load_global_store_data().await?;
        let res = data.recently_updated_objects(cursor, limit);
        res
    }

    async fn upsert_object_metadatas(
        &self,
        updates: HashMap<FullObjectId<'_>, ObjectMetadataUpdate>,
    ) -> Result<bool> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.upsert_object_metadatas(updates);
        self.write_global_store_data(data).await?;
        res
    }

    async fn purge_object_metadatas(&self, object_ids: &[FullObjectId]) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.purge_object_metadatas(object_ids)?;
        self.write_global_store_data(data).await
    }

    async fn query_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_ids: &[Uuid],
        ids: &[FullRowId<'_, '_>],
    ) -> Result<HashMap<FullRowIdOwned, Uuid>> {
        let data = self.load_global_store_data().await?;
        data.query_id_segment_membership(membership_type, segment_ids, ids)
    }

    async fn add_id_segment_membership(
        &self,
        membership_type: IdSegmentMembershipType,
        segment_id_to_entries: HashMap<Uuid, Vec<FullRowId<'_, '_>>>,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.add_id_segment_membership(membership_type, segment_id_to_entries)?;
        self.write_global_store_data(data).await?;
        Ok(res)
    }

    async fn purge_id_segment_membership(&self, segment_ids: &[Uuid]) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.purge_id_segment_membership(segment_ids)?;
        self.write_global_store_data(data).await
    }

    async fn copy_id_segment_membership(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.copy_id_segment_membership(src_segment_ids, dst_segment_id)?;
        self.write_global_store_data(data).await
    }

    async fn query_segment_wal_entries_batch(
        &self,
        segment_id: Uuid,
        cursor: Option<SegmentWalEntriesCursor>,
        limit: Option<i64>,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<SegmentWalEntry>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_wal_entries_batch(segment_id, cursor, limit, is_compacted_filter)
    }

    async fn query_segment_wal_entries_existence(
        &self,
        segment_id_cursors: &[(Uuid, Option<SegmentWalEntriesCursor>)],
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<bool>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_wal_entries_existence(segment_id_cursors, is_compacted_filter)
    }

    async fn query_segment_wal_entries_xact_id_statistic(
        &self,
        segment_ids: &[Uuid],
        statistic: SegmentWalEntriesXactIdStatistic,
        is_compacted_filter: Option<bool>,
    ) -> Result<Vec<Option<TransactionId>>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_wal_entries_xact_id_statistic(
            segment_ids,
            statistic,
            is_compacted_filter,
        )
    }

    async fn upsert_segment_wal_entries(
        &self,
        segment_id_to_wal_filename_to_entries: HashMap<
            Uuid,
            HashMap<Uuid, Vec<UpsertSegmentWalEntry>>,
        >,
    ) -> Result<u64> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.upsert_segment_wal_entries(segment_id_to_wal_filename_to_entries)?;
        self.write_global_store_data(data).await?;
        Ok(res)
    }

    async fn update_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_id_xact_id_wal_filenames: &[(Uuid, TransactionId, Uuid)],
        is_compacted: bool,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.update_segment_wal_entries_is_compacted_non_atomic(
            segment_id_xact_id_wal_filenames,
            is_compacted,
        );
        self.write_global_store_data(data).await?;
        res?;
        Ok(())
    }

    async fn update_all_segment_wal_entries_is_compacted_non_atomic(
        &self,
        segment_ids: &[Uuid],
        is_compacted: bool,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res =
            data.update_all_segment_wal_entries_is_compacted_non_atomic(segment_ids, is_compacted);
        self.write_global_store_data(data).await?;
        res?;
        Ok(())
    }

    async fn purge_segment_wal_entries(&self, segment_ids: &[Uuid]) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.purge_segment_wal_entries(segment_ids)?;
        self.write_global_store_data(data).await
    }

    async fn copy_segment_wal_entries(
        &self,
        src_segment_ids: &[Uuid],
        dst_segment_id: Uuid,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.copy_segment_wal_entries(src_segment_ids, dst_segment_id)?;
        self.write_global_store_data(data).await
    }

    async fn query_last_index_operations(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<Vec<Option<LastIndexOperationResult>>> {
        let data = self.load_global_store_data().await?;
        data.query_last_index_operations(segment_ids)
    }

    async fn upsert_last_index_operation(
        &self,
        segment_id: Uuid,
        operation: LastIndexOperation,
        op_token: Uuid,
        op_token_opts: LastIndexOpTokenOpts,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.upsert_last_index_operation(segment_id, operation, op_token, op_token_opts)?;
        self.write_global_store_data(data).await
    }

    async fn bump_last_index_operation_updated_ts(
        &self,
        segment_id: Uuid,
        op_token: Uuid,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.bump_last_index_operation_updated_ts(segment_id, op_token)?;
        self.write_global_store_data(data).await
    }

    async fn query_field_statistics(
        &self,
        segment_ids: &[Uuid],
        field_names: &[&str],
    ) -> Result<HashMap<Uuid, HashMap<String, SegmentFieldStatistics>>> {
        let data = self.load_global_store_data().await?;
        data.query_field_statistics(segment_ids, field_names)
    }

    async fn upsert_field_statistics(
        &self,
        segment_id_field_name_statistics: Vec<(Uuid, &str, SegmentFieldStatistics)>,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.upsert_field_statistics(segment_id_field_name_statistics);
        self.write_global_store_data(data).await
    }

    async fn upsert_segment_metadatas_and_field_statistics(
        &self,
        updates: HashMap<Uuid, (SegmentMetadataUpdate, Vec<(&str, SegmentFieldStatistics)>)>,
    ) -> Result<bool> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.upsert_segment_metadatas_and_field_statistics(updates)?;
        self.write_global_store_data(data).await?;
        Ok(res)
    }

    async fn query_purged_wal_filenames(
        &self,
        segment_id: Uuid,
        wal_filenames: &[Uuid],
    ) -> Result<Vec<Uuid>> {
        let data = self.load_global_store_data().await?;
        data.query_purged_wal_filenames(segment_id, wal_filenames)
    }

    async fn count_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        let data = self.load_global_store_data().await?;
        data.count_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)
    }

    async fn delete_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        if segment_ids.is_empty() {
            return Ok(0);
        }

        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let num_deleted = data.delete_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)?;
        self.write_global_store_data(data).await?;
        Ok(num_deleted)
    }

    async fn purge_deleted_segment_wal_entries(
        &self,
        segment_ids: &[Uuid],
        expiration_seconds: u64,
    ) -> Result<u64> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let num_purged = data.purge_deleted_segment_wal_entries(segment_ids, expiration_seconds)?;
        self.write_global_store_data(data).await?;
        Ok(num_purged)
    }

    async fn restore_segment_wal_entries_up_to_xact_id(
        &self,
        segment_ids: &[Uuid],
        xact_id: TransactionId,
    ) -> Result<u64> {
        if segment_ids.is_empty() {
            return Ok(0);
        }

        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let num_restored = data.restore_segment_wal_entries_up_to_xact_id(segment_ids, xact_id)?;
        self.write_global_store_data(data).await?;
        Ok(num_restored)
    }

    async fn query_vacuum_index_segment_ids(
        &self,
        limit: usize,
        max_last_successful_start_minus_last_written_seconds: i64,
        vacuum_period_seconds: i64,
        start_ts: DateTime<Utc>,
        object_ids: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<Uuid>> {
        let data = self.load_global_store_data().await?;
        data.query_vacuum_index_segment_ids(
            limit,
            max_last_successful_start_minus_last_written_seconds,
            vacuum_period_seconds,
            start_ts,
            object_ids,
        )
    }

    async fn query_segment_vacuum_state(
        &self,
        segment_ids: &[Uuid],
    ) -> Result<HashMap<Uuid, SegmentVacuumState>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_vacuum_state(segment_ids)
    }

    async fn upsert_segment_vacuum_last_successful_start_ts(
        &self,
        segment_ids: &[Uuid],
        vacuum_type: VacuumType,
        last_successful_start_ts: DateTime<Utc>,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.upsert_segment_vacuum_last_successful_start_ts(
            segment_ids,
            vacuum_type,
            last_successful_start_ts,
        )?;
        self.write_global_store_data(data).await
    }

    async fn upsert_segment_last_written_ts(
        &self,
        segment_ids: &[Uuid],
        last_written_ts: Option<DateTime<Utc>>,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.upsert_segment_last_written_ts(segment_ids, last_written_ts)?;
        self.write_global_store_data(data).await
    }

    async fn list_object_ids(
        &self,
        object_id_cursor: Option<FullObjectId<'_>>,
        limit: usize,
        object_ids_filter: Option<&[FullObjectId<'_>]>,
    ) -> Result<Vec<FullObjectIdOwned>> {
        let data = self.load_global_store_data().await?;
        data.list_object_ids(object_id_cursor, limit, object_ids_filter)
    }

    async fn list_object_segment_ids(
        &self,
        object_id: FullObjectId<'_>,
        segment_id_cursor: Uuid,
        limit: usize,
    ) -> Result<Vec<Uuid>> {
        let data = self.load_global_store_data().await?;
        data.list_object_segment_ids(object_id, segment_id_cursor, limit)
    }

    async fn list_object_ids_segment_ids(
        &self,
        object_ids: &[FullObjectId<'_>],
        limit: usize,
    ) -> Result<Vec<(FullObjectIdOwned, Uuid)>> {
        let data = self.load_global_store_data().await?;
        data.list_object_ids_segment_ids(object_ids, limit)
    }

    async fn query_time_based_retention_state(&self) -> Result<TimeBasedRetentionState> {
        let data = self.load_global_store_data().await?;
        data.query_time_based_retention_state()
    }

    async fn upsert_time_based_retention_state(
        &self,
        state: &TimeBasedRetentionState,
    ) -> Result<()> {
        let mut data = self.load_global_store_data().await?;
        data.upsert_time_based_retention_state(state)?;
        self.write_global_store_data(data).await
    }

    async fn query_segment_task_infos(&self, segment_ids: &[Uuid]) -> Result<Vec<TaskInfos>> {
        let data = self.load_global_store_data().await?;
        data.query_segment_task_infos(segment_ids)
    }

    async fn upsert_segment_task_info(&self, segment_ids: &[Uuid], info: &TaskInfo) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.upsert_segment_task_info(segment_ids, info)?;
        self.write_global_store_data(data).await
    }

    async fn query_unbackfilled_tracking_entries_ordered(
        &self,
        has_completed_initial_backfill: bool,
        cursor: Option<BackfillTrackingEntryId<'_>>,
        limit: usize,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        let data = self.load_global_store_data().await?;
        data.query_unbackfilled_tracking_entries_ordered(
            has_completed_initial_backfill,
            cursor,
            limit,
        )
    }

    async fn update_backfill_tracking_entries(
        &self,
        updates: Vec<(BackfillTrackingEntryId<'_>, BackfillTrackingEntryUpdate)>,
    ) -> Result<Vec<BackfillTrackingEntry>> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        let res = data.update_backfill_tracking_entries(updates)?;
        self.write_global_store_data(data).await?;
        Ok(res)
    }

    async fn query_backfill_tracking_entries_by_ids(
        &self,
        entries: &[BackfillTrackingEntryId<'_>],
    ) -> Result<Vec<Option<BackfillTrackingEntry>>> {
        let data = self.load_global_store_data().await?;
        data.query_backfill_tracking_entries_by_ids(entries)
    }

    async fn query_backfill_brainstore_objects(
        &self,
        tracking_entries: &[BackfillTrackingEntryId<'_>],
        is_logs2: bool,
        min_sequence_id: i64,
        max_sequence_id: i64,
    ) -> Result<Vec<Vec<BackfillBrainstoreObject>>> {
        let data = self.load_global_store_data().await?;
        data.query_backfill_brainstore_objects(
            tracking_entries,
            is_logs2,
            min_sequence_id,
            max_sequence_id,
        )
    }

    #[cfg(test)]
    async fn testing_only_insert_backfill_data(
        &self,
        tracking_entries: Vec<BackfillTrackingEntry>,
        brainstore_objects: Vec<TestingOnlyBackfillBrainstoreObjectAtom>,
    ) -> Result<()> {
        let _guard = self.write_lock_global_store_data().await?;
        let mut data = self.load_global_store_data().await?;
        data.testing_only_insert_backfill_data(tracking_entries, brainstore_objects)?;
        self.write_global_store_data(data).await
    }

    async fn status(&self) -> Result<String> {
        Ok("DirectoryGlobalStore is ok".into())
    }
}

impl Instrumented for DirectoryGlobalStore {
    fn enable_timing(&self) {
        self.directory.enable_timing();
    }

    fn reset_timing(&self) {
        self.directory.reset_timing();
    }

    fn timers(&self) -> Vec<Arc<crate::timer::TimerManager>> {
        self.directory.timers()
    }
}
