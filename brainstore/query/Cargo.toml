[package]
name = "query"
version = "0.1.0"
edition = "2021"

[features]
default = []
expect-tests = ["dep:jaq-core", "dep:jaq-json", "dep:jaq-std"]

[dependencies]
query-macros = { path = "../query-macros" }
btql = { path = "../btql" }
util = { path = "../util" }
tantivy = { path = "../tantivy" }
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0.128"
storage = { path = "../storage" }
thiserror = "1.0"
chrono = "0.4.38"
base64 = "0.22.1"
rayon = "1.10.0"
tempfile = "3.13.0"
log = "0.4.22"
env_logger = "0.11.5"
similar = "2.6.0"
similar-asserts = "1.6.0"
regex = "1.11.0"
time = { version = "0.3.37", features = ["formatting", "macros"] }
tokio = "1.40.0"
tokio-stream = "0.1.16"
tracing = { path = "../tracing" }
async-stream = "0.3.6"
async-trait = "0.1.83"
clap = { version = "4.5.18", features = ["derive"] }
lazy_static = "1.5.0"
itertools = "0.13.0"
sha2 = "0.10.8"
rand = "0.8.5"
arc-swap = "1.7.1"
tantivy-fst = "0.5.0"
sketches-ddsketch = "0.3.0"
jaq-core = { version = "2.1.0", optional = true }
jaq-json = { version = "1.1.0", features = ["serde_json"], optional = true }
jaq-std = { version = "2.1.0", optional = true }

hashbrown = { version = "0.15.2", features = ["raw-entry"] }
gxhash = "3.4.1"

[dev-dependencies]
rand = "0.8"
rand_distr = "0.4"
# These have to be duplicated because they're used if we're running tests or have the expect tests feature.
jaq-core = { version = "2.1.0"}
jaq-json = { version = "1.1.0", features = ["serde_json"]}
jaq-std = { version = "2.1.0"}
criterion = "0.3"

[[bench]]
name = "interpret_expr"
harness = false
