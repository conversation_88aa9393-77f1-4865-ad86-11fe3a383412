use serde::{Deserialize, Serialize};

// The transaction id is a 64-bit integer with the following structure:
//     - Bits 0-15: a monotonically increasing counter (mod 2^16) to prevent collisions
//     - Bits 16-47: the current time in seconds since the Unix epoch
//     - Bits 48-63: a fixed value (0x0de1) that "fills" up the top 16 bits to ensure the
//         number uses 19 digits (maximum for a 64-bit #), making the numbers
//         lexicographically sortable as both strings and integers.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Hash, PartialOrd, Ord)]
pub struct TransactionId(pub u64);

impl TransactionId {
    pub fn from_timestamp(timestamp: u64, counter: u16) -> Self {
        TransactionId((0x0de1 << 48) | ((timestamp & 0xffffffffffff) << 16) | (counter as u64))
    }

    pub fn to_timestamp_and_counter(&self) -> (u64, u16) {
        let timestamp = (self.0 >> 16) & 0xffffffff;
        let counter = self.0 & 0xffff;
        (timestamp, counter as u16)
    }

    pub fn from_time_ago(ago: std::time::Duration, counter: u16) -> TransactionId {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap();
        let timestamp = now.as_secs() - ago.as_secs();
        TransactionId::from_timestamp(timestamp, counter)
    }
}

impl std::fmt::Display for TransactionId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::str::FromStr for TransactionId {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        let id = s.parse::<u64>()?;
        Ok(TransactionId(id))
    }
}

impl<'de> Deserialize<'de> for TransactionId {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let value = deserializer.deserialize_any(StringOrNumberVisitor)?;
        Ok(TransactionId(value))
    }
}

struct StringOrNumberVisitor;

impl<'de> serde::de::Visitor<'de> for StringOrNumberVisitor {
    type Value = u64;

    fn expecting(&self, formatter: &mut std::fmt::Formatter) -> std::fmt::Result {
        formatter.write_str("a string or number")
    }

    fn visit_str<E>(self, value: &str) -> Result<u64, E>
    where
        E: serde::de::Error,
    {
        value.parse::<u64>().map_err(serde::de::Error::custom)
    }

    fn visit_u64<E>(self, value: u64) -> Result<u64, E>
    where
        E: serde::de::Error,
    {
        Ok(value)
    }
}

#[derive(Default, Debug, Clone, Copy, PartialEq, Eq, Hash, PartialOrd, Ord)]
#[repr(transparent)]
pub struct PaginationKey(pub u64);

// Keep this in sync with the pagination key logic in api-ts/src/brainstore/wal.ts.
impl std::fmt::Display for PaginationKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        // We left-pad the number with zeroes to ensure that it is lexicographically sortable. Also
        // prepend with a 'p' to make it easier to visually distinguish from other numbers.
        write!(f, "p{:0>20}", self.0)
    }
}

impl std::str::FromStr for PaginationKey {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        let id = s[1..].parse::<u64>()?;
        Ok(PaginationKey(id))
    }
}

impl Serialize for PaginationKey {
    fn serialize<S: serde::Serializer>(&self, serializer: S) -> Result<S::Ok, S::Error> {
        serializer.serialize_str(&self.to_string())
    }
}

impl<'de> Deserialize<'de> for PaginationKey {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        deserializer.deserialize_str(PaginationKeyVisitor)
    }
}

struct PaginationKeyVisitor;

impl<'de> serde::de::Visitor<'de> for PaginationKeyVisitor {
    type Value = PaginationKey;

    fn expecting(&self, formatter: &mut std::fmt::Formatter) -> std::fmt::Result {
        formatter.write_str("a PaginationKey string")
    }

    fn visit_str<E>(self, value: &str) -> Result<PaginationKey, E>
    where
        E: serde::de::Error,
    {
        value
            .parse::<PaginationKey>()
            .map_err(serde::de::Error::custom)
    }
}

impl Into<crate::Value> for PaginationKey {
    fn into(self) -> crate::Value {
        crate::Value::String(self.to_string())
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PaginationKeyCounter {
    WithinXactRowNum(u16),
    IndependentCounter(u32),
}

pub fn make_pagination_key(xact_id: TransactionId, counter: PaginationKeyCounter) -> PaginationKey {
    let xact_id = xact_id.0;
    match counter {
        PaginationKeyCounter::WithinXactRowNum(row_num) => {
            // Use both the timestamp and counter components of the xact_id.
            PaginationKey(((xact_id & 0xffffffffffff) << 16) | (row_num as u64))
        }
        // Keep this in sync with the pagination key logic in api-ts/src/brainstore/wal.ts.
        PaginationKeyCounter::IndependentCounter(counter) => {
            // Use just the timestamp component of the xact_id.
            PaginationKey((((xact_id >> 16) & 0xffffffff) << 32) | (counter as u64))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_make_pagination_key_len() {
        let counters = [
            PaginationKeyCounter::WithinXactRowNum(u16::MIN),
            PaginationKeyCounter::WithinXactRowNum(u16::MAX),
            PaginationKeyCounter::IndependentCounter(u32::MIN),
            PaginationKeyCounter::IndependentCounter(u32::MAX),
        ];
        let xact_ids = [TransactionId(u64::MIN), TransactionId(u64::MAX)];
        for xact_id in &xact_ids {
            for counter in &counters {
                let key = make_pagination_key(*xact_id, *counter);
                assert_eq!(key.to_string().len(), 21);
            }
        }
    }

    #[test]
    fn test_xact_id_from_timestamp() {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let xact = TransactionId::from_timestamp(timestamp, 1);
        let (timestamp_rt, counter) = xact.to_timestamp_and_counter();
        assert_eq!(timestamp, timestamp_rt);
        assert_eq!(counter, 1);
    }
}
