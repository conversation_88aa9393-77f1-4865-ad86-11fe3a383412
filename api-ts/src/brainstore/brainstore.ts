import {
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
} from "@braintrust/local/api-schema";
import {
  RealtimeState,
  realtimeStateSchema,
  FreshnessState,
  freshnessStateSchema,
} from "@braintrust/local/app-schema";
import { _urljoin, isEmpty } from "@braintrust/core";
import {
  BRAINSTORE_LOG_REPRO_COMMANDS,
  BRAINSTORE_ENABLED,
  BRAINSTORE_URL,
  RESPONSE_BUCKET_ACCESS_KEY_ID,
  RESPONSE_BUCKET_REGION,
  RESPONSE_BUCKET_S3_ENDPOINT,
  RESPONSE_BUCKET_SECRET_ACCESS_KEY,
  BRAINSTORE_DISABLE_COMPACTION,
  BRAINSTORE_DEFAULT,
  BRAINSTORE_INSERT_ROW_REFS,
  INSERT_LOGS2,
  BRAINSTORE_WRITER_URL,
  BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL,
} from "../env";
import {
  BadRequestError,
  extractErrorText,
  HTTPError,
  InternalServerError,
  isVersionQuerySupported,
  postDefaultHeaders,
  wrapZodError,
} from "../util";
import {
  BoundAlias,
  BoundQuery,
  getResultSchema,
  ResponseSchema,
} from "@braintrust/btql/binder";
import { ObjectType, objectTypeSchema } from "../schema";
import { z } from "zod";
import { customFetch } from "../custom_fetch";
import { inferSchema, JSONSchemaObject } from "@braintrust/btql/schema";
import { ModelCostsMap } from "../summary";
import {
  blockBackfillObjects,
  isBlockBackfillObject,
} from "../block_backfill_objects";
import {
  logCounter,
  logHistogram,
  makeBaggage,
  otelTraced,
} from "../instrumentation/api";
import { getLogger } from "../instrumentation/logger";
import { Attributes, Span } from "@opentelemetry/api";
import { isObject } from "lodash";

export type BrainstoreWALEntry = Record<string, unknown>;

export function brainstoreEnabled(): boolean {
  return BRAINSTORE_URL !== undefined && BRAINSTORE_ENABLED;
}

export function brainstoreDefault(): "default" | "force" | undefined {
  if (!brainstoreEnabled() || !BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL) {
    return undefined;
  }
  return BRAINSTORE_DEFAULT;
}

export function brainstoreReaderUrl(): string {
  if (!brainstoreEnabled()) {
    throw new InternalServerError("Brainstore is not enabled");
  }
  return BRAINSTORE_URL!;
}

export function brainstoreWriterUrl(): string {
  if (!brainstoreEnabled()) {
    throw new InternalServerError("Brainstore is not enabled");
  }
  return BRAINSTORE_WRITER_URL ?? BRAINSTORE_URL!;
}

export function assertBrainstoreEnabled() {
  if (!brainstoreEnabled()) {
    throw new BadRequestError("Brainstore is not enabled");
  }
}

function isPgRestrictedQueryObject(
  objectType: ObjectType,
  objectId: string,
): boolean {
  return !!(
    brainstoreDefault() === "force" &&
    shouldUseBrainstore(objectType) &&
    // Since version queries must go through postgres for now, we cannot support
    // settings which restrict postgres queries.
    !isVersionQuerySupported(objectType) &&
    // Blocked objects must be queried through postgres, so we cannot support
    // settings which restrict postgres queries.
    !isBlockBackfillObject(objectType, objectId)
  );
}

export function canContainRowRefs(
  objectType: ObjectType,
  objectId: string,
): boolean {
  return !!(
    BRAINSTORE_INSERT_ROW_REFS &&
    isPgRestrictedQueryObject(objectType, objectId)
  );
}

export function canPgInsertLogs2(
  objectType: ObjectType,
  objectId: string,
): boolean {
  return !!(INSERT_LOGS2 && isPgRestrictedQueryObject(objectType, objectId));
}

// Run 100 process commands at once. This is embarrassingly parallel but still helpful
// to throttle craziness
const DEFAULT_BATCH_SIZE = 100;

export async function processWal({
  callerIdentifier,
  objectIds,
  batchSize,
  sequenceIdStart,
  sequenceIdEnd,
  endXactId,
  markAsCompacted,
  readLogs2,
  readComments,
  disableTracing,
}: {
  callerIdentifier: string;
  objectIds: string[];
  batchSize?: number;
  sequenceIdStart: number;
  sequenceIdEnd: number;
  endXactId?: string;
  markAsCompacted?: boolean;
  readLogs2?: boolean;
  readComments?: boolean;
  disableTracing?: boolean;
}): Promise<void> {
  if (!brainstoreEnabled()) {
    throw new InternalServerError("Brainstore not configured");
  }

  batchSize = batchSize ?? DEFAULT_BATCH_SIZE;
  const blockBackfillObjectIds = blockBackfillObjects.map(
    (obj) => obj.brainstoreObjectId,
  );
  objectIds = objectIds.filter((x) => !blockBackfillObjectIds.includes(x));

  getLogger().debug(
    { callerIdentifier, objectCount: objectIds.length },
    `[BrainstoreTS] Processing ${objectIds.length} objects into brainstore`,
  );

  for (let i = 0; i < objectIds.length; i += batchSize) {
    const batch = objectIds.slice(i, i + batchSize);
    const start = new Date();
    let segmentIds: string[];
    try {
      segmentIds = await runBrainstore({
        path: "wal/process",
        args: {
          start_sequence_id: sequenceIdStart,
          end_sequence_id: sequenceIdEnd,
          read_logs2: readLogs2,
          read_comments: readComments,
          object_ids: batch,
          // Explicitly override the start_xact_id to 0 to ensure we don't skip over
          // rows which start before each object's last_processed_xact_id. We
          // might encounter old transaction ids in later sequence ID batches,
          // because transactions are not strictly ordered with respect to
          // sequence ID.
          //
          // The actual value of start_xact_id doesn't matter, as long as it's
          // less than or equal to whatever sequence ID range we are processing.
          start_xact_id: 0,
          ...(endXactId !== undefined ? { end_xact_id: endXactId } : {}), // Don't specify if not provided
          mark_as_compacted: markAsCompacted,
          source: callerIdentifier,
        },
        schema: z.array(z.string()),
        isWrite: true,
        disableTracing,
      });
    } catch (e) {
      getLogger().error(
        { error: extractErrorText(e), objectIds },
        `[BrainstoreTS] Error while processing ${objectIds.join(", ")}: ${extractErrorText(e)}`,
      );
      throw e;
    }
    const end = new Date();
    getLogger().debug(
      {
        callerIdentifier,
        batchSize: batch.length,
        durationMs: end.getTime() - start.getTime(),
      },
      `[BrainstoreTS] Processed batch of objects`,
    );

    if (segmentIds.length > 0 && !BRAINSTORE_DISABLE_COMPACTION) {
      (async () => {
        getLogger().debug(
          { callerIdentifier, segmentCount: segmentIds.length },
          `[BrainstoreTS] Running compaction`,
        );
        await runBrainstore({
          path: "wal/compact",
          args: {
            segment_ids: segmentIds,
            try_acquire: true,
            queue: true,
          },
          schema: z.null(),
          isWrite: true,
          disableTracing,
        });
        const end = new Date();
        getLogger().debug(
          {
            callerIdentifier,
            segmentCount: segmentIds.length,
            durationMs: end.getTime() - start.getTime(),
          },
          `[BrainstoreTS] Compacted segments`,
        );
      })().catch((e) => {
        getLogger().error(
          { error: e },
          "[BrainstoreTS] Error during compaction",
        );
      });
    }
  }
  if (endXactId) {
    getLogger().debug(
      { callerIdentifier, sequenceIdEnd, endXactId },
      `[BrainstoreTS] Done processing WAL up to sequence id, bounded by xact_id`,
    );
  } else {
    getLogger().debug(
      { callerIdentifier, sequenceIdEnd },
      `[BrainstoreTS] Done processing WAL up to sequence id`,
    );
  }
}

export function shouldUseBrainstore(objectType: ObjectType): boolean {
  switch (objectType) {
    case "project_logs":
    case "playground_logs":
    case "experiment":
    case "dataset":
    case "project":
      return true;
    default:
      return false;
  }
}

export const queryResultSchema = z.object({
  rows: z.array(z.record(z.any())),
  errors: z.array(z.string()),
  cursor: z.string().nullish(),
  plan: z.string().nullish(),
  realtime_state: z.unknown().nullish(),
  freshness_state: z.unknown().nullish(),
});
export type BrainstoreQueryResult = z.infer<typeof queryResultSchema>;

const BRAINSTORE_RETRIES = 3;
const RETRY_DELAY_MS = 100;
const BRAINSTORE_RETRY_DELAY_MULTIPLIER = 2;
const DOWN_THRESHOLD_MS = 50;

export async function runBrainstoreQuery({
  query,
  explain,
  realtime,
  realtimeReadTimeoutMs,
  tableType,
  appOrigin,
  ctxToken,
  inferenceDepth,
  setTraceIdHeader,
  tzOffset,
  modelCosts,
  tracingMetadata,
  metricMetadata,
  queryTimeoutSeconds,
}: {
  query: BoundQuery;
  explain: "explain" | "include_plan" | null;
  realtime: boolean;
  realtimeReadTimeoutMs?: number;
  tableType: "main" | "audit_log";
  appOrigin: string;
  ctxToken?: string;
  inferenceDepth?: number;
  setTraceIdHeader?: (traceId: string) => void;
  tzOffset?: number;
  modelCosts?: ModelCostsMap;
  tracingMetadata?: Attributes;
  metricMetadata?: Attributes;
  queryTimeoutSeconds?: number;
}): Promise<
  | {
      explain: unknown;
    }
  | {
      rows: Record<string, unknown>[];
      resultSchema: ResponseSchema;
      cursor: string | undefined;
      plan?: string;
      realtime_state?: RealtimeState;
      freshness_state?: FreshnessState;
    }
> {
  if (tableType !== "main") {
    throw new BadRequestError("Brainstore queries are not supported in duckdb");
  }

  if (query.from?.name === "project") {
    const projectIds = query.from.objects ?? [];
    if (projectIds.length === 0) {
      throw new BadRequestError(
        "Project queries must specify at least one project",
      );
    }

    const objects = (
      await Promise.all(
        projectIds.map(async (projectId) => {
          const experimentIds = await fetchProjectExperimentIds({
            projectId,
            appOrigin,
            ctxToken,
          });
          return [
            `project_logs:${projectId}`,
            ...experimentIds.map((e) => `experiment:${e}`),
          ];
        }),
      )
    ).flat();

    query.from = {
      name: "object",
      objects,
    };
  }

  if (
    (!query.sort || query.sort.length === 0) &&
    "select" in query &&
    query.select
  ) {
    query.sort = [
      {
        dir: "desc",
        expr: {
          op: "field",
          name: ["_pagination_key"],
          type: { type: "string" },
        },
      },
    ];
  }

  if (explain === "explain") {
    const result = await runBrainstore({
      path: "btql/query",
      args: {
        query,
        bound: true,
        // TODO(manu): Change this to 'skip_realtime_wal_entries' and remove the
        // legacy parameter in the brainstore CLI once sufficient time has
        // passed since 2025-06-02.
        skip_unprocessed_wal_entries: !realtime,
        realtime_read_timeout_ms: realtimeReadTimeoutMs,
        tz_offset: tzOffset,
        ...(explain ? { stage: "plan" } : {}),
      },
      schema: z.object({ plan: z.string() }),
      setTraceIdHeader,
    });
    return {
      explain: result.plan,
    };
  }

  let rows: z.infer<typeof queryResultSchema> | undefined = undefined;

  for (let i = 0; i < BRAINSTORE_RETRIES; i++) {
    const startTime = Date.now();
    rows = await otelTraced("brainstore_query", async (span) => {
      if (tracingMetadata) {
        span.setAttributes(tracingMetadata);
      }
      try {
        return await runBrainstoreBtqlRequest({
          query,
          explain,
          realtime,
          realtimeReadTimeoutMs,
          tzOffset,
          modelCosts,
          tracingMetadata,
          metricMetadata,
          setTraceIdHeader,
          queryTimeoutSeconds,
        });
      } catch (e) {
        const duration = Date.now() - startTime;
        if (i === BRAINSTORE_RETRIES - 1 || duration > DOWN_THRESHOLD_MS) {
          throw e;
        }
        const sleepTime =
          RETRY_DELAY_MS * BRAINSTORE_RETRY_DELAY_MULTIPLIER ** i;
        getLogger().warn(
          {
            attempt: i + 1,
            maxRetries: BRAINSTORE_RETRIES,
            error: e,
            sleepTimeMs: sleepTime,
          },
          `Failed to run BrainstoreQuery, retrying`,
        );
        await new Promise((resolve) => setTimeout(resolve, sleepTime));
        return undefined;
      }
    });
    if (rows) {
      break;
    }
  }

  if (rows !== undefined && rows.errors.length > 0) {
    getLogger().error(
      { errors: rows.errors.join("\n") },
      "Failed to run BrainstoreQuery",
    );
    throw new InternalServerError(
      `Failed to run BrainstoreQuery: ${rows.errors.join("\n")}`,
    );
  } else if (rows === undefined) {
    throw new InternalServerError(
      "Failed to run BrainstoreQuery (ran out of retries)",
    );
  }

  const schema = getResultSchema(query);

  if (inferenceDepth !== undefined) {
    for (const row of rows.rows) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      schema.items = inferSchema({
        schema: schema.items,
        value: row,
        depth: inferenceDepth,
      }) as typeof schema.items;
    }
  } else if ("pivot" in query && query.pivot && query.pivot.length > 0) {
    const pivotAliases = query.pivot.map((p: BoundAlias) => p.alias);
    for (const row of rows.rows) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      updatePivotSchema({
        pivotAliases,
        schema: schema.items,
        row,
      });
    }
  }

  const realtimeState = realtimeStateSchema
    .nullish()
    .safeParse(rows.realtime_state);
  if (!realtimeState.success) {
    getLogger().warn(
      {
        realtime_state: rows.realtime_state,
        error: extractErrorText(realtimeState.error),
      },
      `Failed to parse realtime state: ${realtimeState.error.message}`,
    );
  } else if (!["on", "disabled"].includes(realtimeState.data?.type ?? "")) {
    logCounter({
      name: "api.brainstore.btql.realtime_failed",
      value: 1,
      attributes: {
        ...metricMetadata,
        exhaust_type: realtimeState.data?.type ?? "unknown",
      },
    });
  }

  const freshnessState = freshnessStateSchema
    .nullish()
    .safeParse(rows.freshness_state);
  if (!freshnessState.success && rows.freshness_state !== undefined) {
    getLogger().warn(
      {
        freshness_state: rows.freshness_state,
        error: extractErrorText(freshnessState.error),
      },
      `Failed to parse freshness state: ${freshnessState.error.message}`,
    );
  }

  return {
    rows: rows.rows,
    resultSchema: schema,
    cursor: rows.cursor ?? undefined,
    plan: rows.plan ?? undefined,
    realtime_state: realtimeState.success
      ? (realtimeState.data ?? undefined)
      : undefined,
    freshness_state: freshnessState.success
      ? (freshnessState.data ?? undefined)
      : undefined,
  };
}

export const brainstoreStreamingRowSchema = z.union([
  z.object({
    row: z.record(z.any()),
  }),
  z.object({
    error: z.string(),
  }),
  z.object({
    info: z.object({
      cursor: z.string().nullish(),
      plan: z.string().nullish(),
      realtime_state: z.unknown().nullish(),
      freshness_state: z.unknown().nullish(),
    }),
  }),
]);

async function runBrainstoreBtqlRequest({
  query,
  explain,
  realtime,
  realtimeReadTimeoutMs,
  tzOffset,
  queryTimeoutSeconds,
  modelCosts,
  tracingMetadata,
  metricMetadata,
  setTraceIdHeader,
}: {
  query: BoundQuery;
  explain: "explain" | "include_plan" | null;
  realtime: boolean;
  realtimeReadTimeoutMs?: number;
  tzOffset: number | undefined;
  queryTimeoutSeconds?: number;
  modelCosts?: ModelCostsMap;
  tracingMetadata?: Attributes;
  metricMetadata?: Attributes;
  setTraceIdHeader?: (traceId: string) => void;
}): Promise<BrainstoreQueryResult> {
  logCounter({
    name: "api.brainstore.btql.num_queries",
    value: 1,
    attributes: metricMetadata,
  });

  return await otelTraced("brainstore_query_request", async (span) => {
    if (tracingMetadata) {
      span.setAttributes(tracingMetadata);
    }
    const requestStartTime = Date.now();
    const response = await runBrainstoreRequest({
      path: "btql/query",
      args: {
        query,
        bound: true,
        // TODO(manu): Change this to 'skip_realtime_wal_entries' and remove the
        // legacy parameter in the brainstore CLI once sufficient time has
        // passed since 2025-06-02.
        skip_unprocessed_wal_entries: !realtime,
        realtime_read_timeout_ms: realtimeReadTimeoutMs,
        tz_offset: tzOffset,
        include_plan: explain === "include_plan",
        stream: true,
        ...(!isEmpty(queryTimeoutSeconds)
          ? { query_timeout_seconds: queryTimeoutSeconds }
          : {}),
        ...(modelCosts ? { model_costs: modelCosts } : {}),
      },
      setTraceIdHeader,
    });

    const requestDurationMs = Math.round(Date.now() - requestStartTime);
    logHistogram({
      name: "api.brainstore.btql.request_duration_ms",
      value: requestDurationMs,
      attributes: metricMetadata,
    });

    const streamStartTime = Date.now();
    const { result, bytes } = await otelTraced("stream results", async () => {
      const contentType = response.headers.get("content-type");
      let result: BrainstoreQueryResult;
      let bytes = 0;

      if (contentType === "application/json") {
        result = queryResultSchema.parse(await response.json());
        bytes = parseInt(response.headers.get("content-length") ?? "0");
      } else if (contentType === "application/x-ndjson") {
        result = {
          rows: [],
          errors: [],
          cursor: undefined,
          plan: undefined,
          realtime_state: undefined,
          freshness_state: undefined,
        };
        const body = response.body;
        if (!body) {
          throw new InternalServerError(
            "Brainstore query returned an empty (unreadable) response",
          );
        }
        const onNumBytes = (numBytes: number) => {
          bytes += numBytes;
        };
        const reader = body
          .pipeThrough(makeLineDelimitedStream(onNumBytes))
          .getReader();
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          if (!value) {
            continue;
          }
          if ("error" in value) {
            throw new InternalServerError(
              `Failed to parse streaming brainstore row: ${value.error}`,
            );
          }
          const parsed = brainstoreStreamingRowSchema.safeParse(value.value);
          if (!parsed.success) {
            throw new InternalServerError(
              `Brainstore query returned invalid streaming row: ${parsed.error.message}`,
            );
          }
          const row = parsed.data;
          if ("row" in row) {
            result.rows.push(row.row);
          } else if ("error" in row) {
            result.errors.push(row.error);
          } else if ("info" in row) {
            result.cursor = row.info.cursor;
            result.plan = row.info.plan;
            result.realtime_state = row.info.realtime_state;
            result.freshness_state = row.info.freshness_state;
          }
        }
      } else {
        throw new InternalServerError(
          `Brainstore query returned unknown content type ${contentType}`,
        );
      }
      return { result, bytes };
    });

    const streamDurationMs = Math.round(Date.now() - streamStartTime);
    logHistogram({
      name: "api.brainstore.btql.stream_duration_ms",
      value: streamDurationMs,
      attributes: metricMetadata,
    });

    span.setAttributes({
      "btql.metrics.response_rows": result.rows.length,
      "btql.metrics.bytes": bytes,
      ...(result.realtime_state && isObject(result.realtime_state)
        ? flattenAttributes("btql.realtime_state", result.realtime_state)
        : {}),
    });

    logCounter({
      name: "api.brainstore.btql.num_rows",
      value: result.rows.length,
      attributes: metricMetadata,
    });

    logCounter({
      name: "api.brainstore.btql.num_bytes",
      value: bytes,
      attributes: metricMetadata,
    });

    logCounter({
      name: "api.brainstore.btql.num_errors",
      value: result.errors.length,
      attributes: metricMetadata,
    });

    return result;
  });
}

function flattenAttributes(prefix: string, dict: object): Attributes {
  const ret: Attributes = {};
  for (const [key, value] of Object.entries(dict)) {
    if (value !== undefined) {
      ret[`${prefix}.${key}`] = `${value}`;
    }
  }
  return ret;
}

export async function fetchProjectExperimentIds({
  projectId,
  appOrigin,
  ctxToken,
}: {
  projectId: string;
  appOrigin: string;
  ctxToken?: string;
}): Promise<string[]> {
  const resp = await customFetch(`${appOrigin}/api/actions/getExperimentIds`, {
    method: "POST",
    headers: postDefaultHeaders({ token: ctxToken }),
    body: JSON.stringify({
      function_args: {
        project_id: projectId,
      },
    }),
  });
  const experiments = z
    .array(z.object({ id: z.string() }))
    .parse(await resp.json());

  return experiments.map((e) => e.id);
}

export async function fetchProjectDatasetIds({
  projectId,
  appOrigin,
  ctxToken,
}: {
  projectId: string;
  appOrigin: string;
  ctxToken?: string;
}): Promise<string[]> {
  const resp = await customFetch(`${appOrigin}/api/actions/getDatasetIds`, {
    method: "POST",
    headers: postDefaultHeaders({ token: ctxToken }),
    body: JSON.stringify({
      function_args: {
        project_id: projectId,
      },
    }),
  });
  const datasets = z
    .array(z.object({ id: z.string() }))
    .parse(await resp.json());

  return datasets.map((d) => d.id);
}

// Keep this in sync with the make_brainstore_object_id function in
// storage/src/postgres_query_util.rs.
export function makeBrainstoreObjectId(objectIds: ObjectIdsUnion): string {
  let objectType: ObjectType;
  let objectId: string;
  switch (objectIds[OBJECT_TYPE_FIELD]) {
    case "project_logs":
      objectType = "project_logs";
      objectId = objectIds.project_id;
      break;
    case "experiment":
      objectType = "experiment";
      objectId = objectIds.experiment_id;
      break;
    case "dataset":
      objectType = "dataset";
      objectId = objectIds.dataset_id;
      break;
    case "prompt_session":
      objectType = "prompt_session";
      objectId = objectIds.prompt_session_id;
      break;
    case "project_prompts":
      objectType = "project_prompts";
      objectId = objectIds.project_id;
      break;
    case "project_functions":
      objectType = "project_functions";
      objectId = objectIds.project_id;
      break;
    case "playground_logs":
      objectType = "playground_logs";
      objectId = objectIds.prompt_session_id;
      break;
    default:
      throw new Error(`Invalid object type: ${objectIds[OBJECT_TYPE_FIELD]}`);
  }
  return makeBrainstoreObjectIdFromParts(objectType, objectId);
}

export function makeBrainstoreObjectIdFromParts(
  objectType: ObjectType,
  id: string,
): string {
  return `${objectType}:${id}`;
}

export function parseBrainstoreObjectId(fullObjectId: string): {
  objectType: ObjectType;
  objectId: string;
} {
  const parts = fullObjectId.split(":", 2);
  if (parts.length !== 2) {
    throw new BadRequestError("Invalid object id " + fullObjectId);
  }
  const [rawObjectType, objectId] = parts;
  const objectType = wrapZodError(() => objectTypeSchema.parse(rawObjectType));
  return {
    objectType,
    objectId,
  };
}

export async function runBrainstoreRequest({
  path,
  args,
  timeoutMs,
  setTraceIdHeader,
  noLog,
  isWrite,
  disableTracing,
  method = "POST",
}: {
  path: string;
  args: unknown;
  timeoutMs?: number;
  setTraceIdHeader?: (traceId: string) => void;
  noLog?: boolean;
  isWrite?: boolean;
  disableTracing?: boolean;
  method?: "POST" | "GET";
}): Promise<Response> {
  if (BRAINSTORE_URL === undefined) {
    throw new InternalServerError("BRAINSTORE_URL not configured");
  }
  if (method === "GET" && !isEmpty(args)) {
    throw new InternalServerError("brainstore GET requests cannot have a body");
  }

  const fullPath = _urljoin(
    isWrite ? brainstoreWriterUrl() : brainstoreReaderUrl(),
    path,
  );

  const controller = new AbortController();
  const timeoutId = timeoutMs
    ? setTimeout(() => controller.abort(), timeoutMs)
    : null;

  const executeCommand = async (span?: Span) => {
    try {
      if (BRAINSTORE_LOG_REPRO_COMMANDS && !noLog) {
        getLogger().info(
          `curl -X ${method} '${fullPath}'${
            method === "POST"
              ? ` -H 'Content-Type: application/json' -d ${JSON.stringify(JSON.stringify(args))}`
              : ""
          }`,
        );
      }
      const startTime = Date.now();
      try {
        const baggage = span ? makeBaggage({ span }) : undefined;
        const response = await customFetch(fullPath, {
          method,
          body: method === "POST" ? JSON.stringify(args) : undefined,
          signal: controller.signal,
          headers: {
            "Content-Type": "application/json",
            ...baggage,
          },
        });

        if (!response.ok) {
          const responseText = await response.text();
          getLogger().error(
            {
              path,
              status: response.status,
              statusText: response.statusText,
              elapsedMs: Date.now() - startTime,
              responseText,
            },
            `[BrainstoreTS] request failed`,
          );
          throw new HTTPError(
            response.status,
            `Brainstore ${path} request failed ${response.status} (${response.statusText}) after ${Date.now() - startTime} ms` +
              (BRAINSTORE_LOG_REPRO_COMMANDS || path === "btql/query"
                ? `: ${responseText}`
                : ""),
          );
        }

        if (setTraceIdHeader) {
          const traceId = response.headers.get("x-bt-internal-trace-id");
          if (traceId) {
            setTraceIdHeader(traceId);
          }
        }

        return response;
      } catch (e) {
        const elapsedMs = Date.now() - startTime;
        getLogger().error(
          {
            path,
            elapsedMs,
            error: extractErrorText(e),
          },
          `[BrainstoreTS] failed to run ${path} request after ${elapsedMs}ms: ${extractErrorText(e)}`,
        );
        throw e;
      }
    } finally {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    }
  };

  return await (disableTracing
    ? executeCommand()
    : otelTraced("brainstore", executeCommand));
}

export async function runBrainstore<T>({
  path,
  args,
  schema,
  timeoutMs,
  setTraceIdHeader,
  noLog,
  isWrite,
  disableTracing,
  method = "POST",
}: {
  path: string;
  args: unknown;
  schema: z.ZodSchema<T>;
  timeoutMs?: number;
  setTraceIdHeader?: (traceId: string) => void;
  noLog?: boolean;
  isWrite?: boolean;
  disableTracing?: boolean;
  method?: "POST" | "GET";
}): Promise<T> {
  const response = await runBrainstoreRequest({
    path,
    args,
    timeoutMs,
    setTraceIdHeader,
    noLog,
    isWrite,
    disableTracing,
    method,
  });

  const json = await response.json();
  return schema.parse(json);
}

// XXX Move this to bt services
export function makeS3EnvForBrainstore() {
  const ret: Record<string, string> = {};
  if (RESPONSE_BUCKET_S3_ENDPOINT) {
    ret.AWS_ENDPOINT_URL = RESPONSE_BUCKET_S3_ENDPOINT;
    if (RESPONSE_BUCKET_S3_ENDPOINT.startsWith("http://")) {
      ret.AWS_ALLOW_HTTP = "true";
    }
  }
  if (RESPONSE_BUCKET_ACCESS_KEY_ID) {
    ret.AWS_ACCESS_KEY_ID = RESPONSE_BUCKET_ACCESS_KEY_ID;
  }
  if (RESPONSE_BUCKET_SECRET_ACCESS_KEY) {
    ret.AWS_SECRET_ACCESS_KEY = RESPONSE_BUCKET_SECRET_ACCESS_KEY;
  }
  if (RESPONSE_BUCKET_REGION) {
    ret.AWS_REGION = RESPONSE_BUCKET_REGION;
  }
  return ret;
}

function updatePivotSchema({
  pivotAliases,
  schema,
  row,
}: {
  pivotAliases: string[];
  schema: JSONSchemaObject;
  row: Record<string, unknown>;
}) {
  if (pivotAliases.length === 0) {
    return;
  }

  const alias = pivotAliases[0];

  let currSchema: JSONSchemaObject;

  if (schema.properties && schema.properties[alias]) {
    currSchema = schema.properties[alias];
  } else if (
    schema.additionalProperties &&
    typeof schema.additionalProperties === "object" &&
    "properties" in schema.additionalProperties &&
    typeof schema.additionalProperties.properties === "object" &&
    schema.additionalProperties.properties[alias]
  ) {
    currSchema = schema.additionalProperties.properties[alias];
  } else {
    throw new Error(`Invalid unpivot schema (expect "${alias}" property)`);
  }

  let enumValues: string[] = [];
  if (
    currSchema.propertyNames &&
    typeof currSchema.propertyNames === "object" &&
    "enum" in currSchema.propertyNames &&
    Array.isArray(currSchema.propertyNames.enum)
  ) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    enumValues = currSchema.propertyNames.enum as string[];
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const currRow = row[alias] as Record<string, unknown>;
  if (typeof currRow !== "object" || currRow === null) {
    throw new Error(
      `Invalid unpivot schema (expect "${alias}" property to be an object)`,
    );
  }

  for (const key of Object.keys(currRow)) {
    if (!enumValues.includes(key)) {
      enumValues.push(key);
    }
  }

  currSchema.propertyNames = {
    enum: enumValues,
  };

  for (const key of enumValues) {
    if (key in currRow) {
      updatePivotSchema({
        pivotAliases: pivotAliases.slice(1),
        schema: currSchema,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        row: currRow[key] as Record<string, unknown>,
      });
    }
  }
}

type ParsedJSON =
  | {
      value: unknown;
    }
  | {
      error: unknown;
    };

function tryEnqueueParsedJSON(
  ctrl: TransformStreamDefaultController<ParsedJSON>,
  line: string,
) {
  let parsed: unknown;
  try {
    parsed = JSON.parse(line);
    ctrl.enqueue({ value: parsed });
  } catch (e) {
    ctrl.enqueue({ error: e });
  }
}

function makeLineDelimitedStream(onNumBytes: (numBytes: number) => void) {
  let buf = "";
  const decoder = new TextDecoder();
  return new TransformStream<Uint8Array, ParsedJSON>({
    start() {},
    transform(chunk, ctrl) {
      onNumBytes(chunk.length);
      buf += decoder.decode(chunk, { stream: true }); // accumulate until we see “\n”
      let nl;
      while ((nl = buf.indexOf("\n")) !== -1) {
        const line = buf.slice(0, nl).trim();
        buf = buf.slice(nl + 1);
        if (line) {
          tryEnqueueParsedJSON(ctrl, line);
        }
      }
    },
    flush(ctrl) {
      // final line without “\n”
      if (buf.trim()) {
        tryEnqueueParsedJSON(ctrl, buf);
      }
    },
  });
}
