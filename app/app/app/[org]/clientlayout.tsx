"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { AccessFailed } from "#/ui/access-failed";
import Header from "#/ui/layout/header";
import { RefreshProvider } from "#/utils/refresh-data";
import { OrgProvider, useOrg, useUser } from "#/utils/user";
import { MultiTenantApiURL } from "#/utils/user-types";
import { useEffect, useMemo } from "react";
import {
  type FetchOrgUsersOutput,
  type fetchOrgUsers,
} from "#/utils/org-users";
import { useQueryFunc } from "#/utils/react-query";
import { decodeURIComponentPatched } from "#/utils/url";
import { OrgUsersContext } from "#/utils/org-users-context";
import { Sidenav } from "./sidenav";
import { CommandBar } from "./command-bar";
import { useIsClient } from "#/utils/use-is-client";
import { Spinner } from "#/ui/icons/spinner";
import { FeatureFlagProvider } from "#/lib/feature-flags-provider";
import { IdentifyDatadogOrg } from "#/ui/datadog-rum-provider";

/**
 * This component sets a context in Sentry to flag on-prem users.
 *
 * We use this in the beforeSend callback in sentry.client.config.ts
 * to ensure we don't send anything to Sentry for on-prem users.
 *
 */
function SentryInit() {
  const org = useOrg();
  const apiUrl = org.api_url;

  const { user } = useUser();
  const userEmail = user?.email;

  useEffect(() => {
    const isOnPrem = apiUrl !== MultiTenantApiURL;

    if (Sentry) {
      Sentry.setContext("org", { isOnPrem });

      if (userEmail) {
        Sentry.setUser({ email: userEmail });
      }
    }
  }, [apiUrl, userEmail]);

  return null;
}

export default function ClientLayout({
  org,
  orgUsers: _orgUsers,
  children,
}: {
  org: string;
  orgUsers: FetchOrgUsersOutput;
  children: React.ReactNode;
}) {
  const { data: orgUsers, invalidate: refreshOrgUsers } = useQueryFunc<
    typeof fetchOrgUsers
  >({
    fName: "fetchOrgUsers",
    args: { orgName: org },
    serverData: _orgUsers,
  });

  const orgContext = useMemo(
    () => ({ orgUsers, refreshOrgUsers }),
    [orgUsers, refreshOrgUsers],
  );

  const isClient = useIsClient();

  if (!isClient) {
    return (
      <div className="fixed inset-0 flex items-center justify-center text-primary-500">
        <Spinner />
      </div>
    );
  }

  return (
    <OrgProvider orgName={org}>
      <OrgUsersContext.Provider value={orgContext}>
        <FeatureFlagProvider>
          <SentryInit />
          <RefreshProvider>
            <RenderIfUser>
              <div className="flex flex-1">
                <Sidenav />
                <CommandBar />
                <div className="flex-1 overflow-hidden">
                  <Header orgName={org} />
                  {children}
                  <IdentifyDatadogOrg />
                </div>
              </div>
            </RenderIfUser>
          </RefreshProvider>
        </FeatureFlagProvider>
      </OrgUsersContext.Provider>
    </OrgProvider>
  );
}

const useIsLoading = () => {
  const { status } = useUser();
  return status === "loading";
};

// TODO: I'm sure there's a better way to do this with fancier next.js features
export function RenderIfUser({ children }: { children: React.ReactNode }) {
  const isLoading = useIsLoading();

  if (isLoading) {
    return null;
  }

  return children;
}

export function CheckOrg({
  children,
  params,
}: {
  children?: React.ReactNode;
  params: { org: string };
}) {
  const org = useOrg();
  const org_name = decodeURIComponentPatched(params.org);
  return org.id ? (
    <>{children}</>
  ) : (
    <AccessFailed objectType="Organization" objectName={org_name} />
  );
}
