"use client";

import { useOrgPlan } from "#/app/app/[org]/settings/billing/plans";
import { useIsClient } from "#/utils/use-is-client";
import { useOrg } from "#/utils/user";
import { useUser } from "@clerk/nextjs";
import { datadogRum } from "@datadog/browser-rum";
import { useParams, usePathname } from "next/navigation";
import { useEffect } from "react";

// Extend Window interface to include DD_RUM
declare global {
  interface Window {
    DD_RUM?: {
      getInitConfiguration?: () => unknown;
    };
  }
}

interface DatadogRumProviderProps {
  children: React.ReactNode;
}

function DatadogRumInitializer() {
  const isClient = useIsClient();
  const pathname = usePathname();
  const params = useParams();

  useEffect(() => {
    if (typeof window === "undefined" || !window.DD_RUM) {
      return;
    }

    const reverseParamsObject = Object.entries(params ?? {}).reduce(
      (prev, [key, val]) => ({ ...prev, [val.toString()]: key }),
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {} as Record<string, string>,
    );
    // normalize routes like /app/:org/p/:project
    const route = pathname
      ?.split("/")
      .map((part) => {
        if (reverseParamsObject[part]) {
          return `:${reverseParamsObject[part]}`;
        }
        return part;
      })
      .join("/");
    datadogRum.setViewContext({
      route,
    });
  }, [params, pathname]);

  useEffect(() => {
    // Only initialize in the browser and if we have the required config
    if (
      isClient &&
      process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID &&
      process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN
    ) {
      // Check if already initialized to avoid double initialization
      if (!window.DD_RUM?.getInitConfiguration?.()) {
        datadogRum.init({
          applicationId: process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID,
          clientToken: process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN,
          site: "us5.datadoghq.com",
          env: process.env.NEXT_PUBLIC_DATADOG_ENV || "dev",
          sessionSampleRate: process.env.NODE_ENV === "development" ? 0 : 100,
          sessionReplaySampleRate:
            process.env.NODE_ENV === "development" ? 0 : 100,
          trackUserInteractions: true,
          trackResources: true,
          trackLongTasks: true,
          defaultPrivacyLevel: "mask",
        });

        // Start collecting RUM events
        datadogRum.startSessionReplayRecording();
      }
    }
  }, [isClient]);

  return null;
}

export default function DatadogRumProvider({
  children,
}: DatadogRumProviderProps) {
  return (
    <>
      <DatadogRumInitializer />
      {children}
    </>
  );
}

// const trackCustomEvent = (
//   name: string,
//   attributes?: Record<string, string | number | boolean>,
// ) => {
//   if (typeof window !== "undefined" && window.DD_RUM) {
//     datadogRum.addAction(name, attributes);
//   }
// };

export const trackError = (
  error: Error,
  attributes?: Record<string, string | number | boolean>,
) => {
  if (typeof window !== "undefined" && window.DD_RUM) {
    datadogRum.addError(error, attributes);
  }
};

const setUser = (user: {
  id?: string;
  name?: string;
  email?: string;
  [key: string]: string | number | boolean | undefined;
}) => {
  if (typeof window !== "undefined" && window.DD_RUM) {
    datadogRum.setUser(user);
  }
};

const setOrg = (org: {
  id: string;
  name: string;
  plan?: string;
  [key: string]: string | number | boolean | undefined;
}) => {
  if (typeof window !== "undefined" && window.DD_RUM) {
    datadogRum.setAccount(org);
  }
};

// const setGlobalContextProperty = (
//   key: string,
//   value: string | number | boolean,
// ) => {
//   if (typeof window !== "undefined" && window.DD_RUM) {
//     datadogRum.setGlobalContextProperty(key, value);
//   }
// };

export const IdentifyDatadogUser = () => {
  const { user } = useUser();

  useEffect(() => {
    if (user) {
      setUser({
        id: user.id,
        email: user.emailAddresses[0].emailAddress,
        name: user.fullName ?? undefined,
      });
    }
  }, [user]);

  return null;
};

export const IdentifyDatadogOrg = () => {
  const org = useOrg();
  const plan = useOrgPlan();

  useEffect(() => {
    if (org && org.id) {
      setOrg({
        name: org.name,
        id: org.id,
        api_url: org.api_url,
        proxy_url: org.proxy_url,
        realtime_url: org.realtime_url,
        plan: plan ?? undefined,
      });
    }
  }, [org, plan]);

  return null;
};
